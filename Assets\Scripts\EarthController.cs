using UnityEngine;
using System.Collections;

/// <summary>
/// 地球模型控制器
/// 负责地球的旋转、缩放、交互等功能
/// </summary>
public class EarthController : MonoBehaviour
{
    [Header("旋转设置")]
    public float rotationSpeed = 30f;
    public bool autoRotate = true;
    public Vector3 rotationAxis = Vector3.up;

    [Header("缩放设置")]
    public float minScale = 0.1f;
    public float maxScale = 2.0f;
    public float scaleSpeed = 1.0f;

    [Header("地球组件")]
    public Transform earthSphere;
    public Transform atmosphereLayer;
    public Transform cloudLayer;

    [Header("材质")]
    public Material earthMaterial;
    public Material oceanMaterial;
    public Material atmosphereMaterial;
    public Material cloudMaterial;

    [Header("光照")]
    public Light sunLight;
    public bool enableDayNightCycle = true;
    public float dayNightSpeed = 1.0f;

    private Vector3 lastTouchPosition;
    private bool isDragging = false;
    private float currentScale = 1.0f;
    private GeographyDataManager geographyData;
    private UIManager uiManager;

    void Start()
    {
        InitializeEarth();
        SetupComponents();
    }

    void Update()
    {
        if (autoRotate && !isDragging)
        {
            AutoRotateEarth();
        }

        if (enableDayNightCycle)
        {
            UpdateDayNightCycle();
        }

        HandleInput();
        UpdateCloudAnimation();
    }

    /// <summary>
    /// 初始化地球
    /// </summary>
    void InitializeEarth()
    {
        if (earthSphere == null)
            earthSphere = transform;

        currentScale = transform.localScale.x;
        
        // 设置初始材质
        ApplyMaterials();
        
        Debug.Log("地球控制器已初始化");
    }

    /// <summary>
    /// 设置组件引用
    /// </summary>
    void SetupComponents()
    {
        geographyData = FindObjectOfType<GeographyDataManager>();
        uiManager = FindObjectOfType<UIManager>();

        if (sunLight == null)
        {
            sunLight = FindObjectOfType<Light>();
        }
    }

    /// <summary>
    /// 应用材质
    /// </summary>
    void ApplyMaterials()
    {
        Renderer renderer = earthSphere.GetComponent<Renderer>();
        if (renderer != null && earthMaterial != null)
        {
            renderer.material = earthMaterial;
        }

        // 设置大气层材质
        if (atmosphereLayer != null && atmosphereMaterial != null)
        {
            Renderer atmRenderer = atmosphereLayer.GetComponent<Renderer>();
            if (atmRenderer != null)
            {
                atmRenderer.material = atmosphereMaterial;
            }
        }

        // 设置云层材质
        if (cloudLayer != null && cloudMaterial != null)
        {
            Renderer cloudRenderer = cloudLayer.GetComponent<Renderer>();
            if (cloudRenderer != null)
            {
                cloudRenderer.material = cloudMaterial;
            }
        }
    }

    /// <summary>
    /// 自动旋转地球
    /// </summary>
    void AutoRotateEarth()
    {
        transform.Rotate(rotationAxis * rotationSpeed * Time.deltaTime, Space.World);
    }

    /// <summary>
    /// 更新昼夜循环
    /// </summary>
    void UpdateDayNightCycle()
    {
        if (sunLight != null)
        {
            float angle = Time.time * dayNightSpeed;
            sunLight.transform.rotation = Quaternion.Euler(angle, 30f, 0f);
        }
    }

    /// <summary>
    /// 处理输入
    /// </summary>
    void HandleInput()
    {
        // 处理触摸输入
        if (Input.touchCount == 1)
        {
            Touch touch = Input.GetTouch(0);
            HandleSingleTouch(touch);
        }
        else if (Input.touchCount == 2)
        {
            HandlePinchZoom();
        }

        // 处理鼠标输入（编辑器测试）
        HandleMouseInput();
    }

    /// <summary>
    /// 处理单点触摸
    /// </summary>
    void HandleSingleTouch(Touch touch)
    {
        Vector3 touchPosition = Camera.main.ScreenToWorldPoint(
            new Vector3(touch.position.x, touch.position.y, 10f));

        switch (touch.phase)
        {
            case TouchPhase.Began:
                lastTouchPosition = touchPosition;
                isDragging = true;
                break;

            case TouchPhase.Moved:
                if (isDragging)
                {
                    Vector3 deltaPosition = touchPosition - lastTouchPosition;
                    RotateEarth(deltaPosition);
                    lastTouchPosition = touchPosition;
                }
                break;

            case TouchPhase.Ended:
            case TouchPhase.Canceled:
                isDragging = false;
                break;
        }
    }

    /// <summary>
    /// 处理双指缩放
    /// </summary>
    void HandlePinchZoom()
    {
        Touch touch1 = Input.GetTouch(0);
        Touch touch2 = Input.GetTouch(1);

        Vector2 touch1PrevPos = touch1.position - touch1.deltaPosition;
        Vector2 touch2PrevPos = touch2.position - touch2.deltaPosition;

        float prevTouchDeltaMag = (touch1PrevPos - touch2PrevPos).magnitude;
        float touchDeltaMag = (touch1.position - touch2.position).magnitude;

        float deltaMagnitudeDiff = prevTouchDeltaMag - touchDeltaMag;
        ScaleEarth(-deltaMagnitudeDiff * scaleSpeed * 0.01f);
    }

    /// <summary>
    /// 处理鼠标输入
    /// </summary>
    void HandleMouseInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            lastTouchPosition = Input.mousePosition;
            isDragging = true;
        }
        else if (Input.GetMouseButton(0) && isDragging)
        {
            Vector3 deltaPosition = Input.mousePosition - lastTouchPosition;
            RotateEarth(deltaPosition * 0.1f);
            lastTouchPosition = Input.mousePosition;
        }
        else if (Input.GetMouseButtonUp(0))
        {
            isDragging = false;
        }

        // 鼠标滚轮缩放
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0)
        {
            ScaleEarth(scroll * scaleSpeed);
        }
    }

    /// <summary>
    /// 旋转地球
    /// </summary>
    void RotateEarth(Vector3 deltaPosition)
    {
        float rotationX = deltaPosition.y * rotationSpeed * 0.1f;
        float rotationY = -deltaPosition.x * rotationSpeed * 0.1f;

        transform.Rotate(rotationX, rotationY, 0, Space.World);
    }

    /// <summary>
    /// 缩放地球
    /// </summary>
    void ScaleEarth(float scaleDelta)
    {
        currentScale += scaleDelta;
        currentScale = Mathf.Clamp(currentScale, minScale, maxScale);
        transform.localScale = Vector3.one * currentScale;
    }

    /// <summary>
    /// 更新云层动画
    /// </summary>
    void UpdateCloudAnimation()
    {
        if (cloudLayer != null)
        {
            cloudLayer.Rotate(Vector3.up * 5f * Time.deltaTime);
        }
    }

    /// <summary>
    /// 地球被触摸时的回调
    /// </summary>
    public void OnEarthTouched(Vector3 worldPosition)
    {
        // 将世界坐标转换为地球表面坐标
        Vector3 localPosition = transform.InverseTransformPoint(worldPosition);
        Vector3 spherePosition = localPosition.normalized;

        // 转换为经纬度
        float latitude = Mathf.Asin(spherePosition.y) * Mathf.Rad2Deg;
        float longitude = Mathf.Atan2(spherePosition.z, spherePosition.x) * Mathf.Rad2Deg;

        // 获取地理信息
        if (geographyData != null)
        {
            var locationInfo = geographyData.GetLocationInfo(latitude, longitude);
            if (locationInfo != null && uiManager != null)
            {
                uiManager.ShowLocationInfo(locationInfo);
            }
        }

        Debug.Log($"触摸位置 - 纬度: {latitude:F2}°, 经度: {longitude:F2}°");
    }

    /// <summary>
    /// 设置自动旋转
    /// </summary>
    public void SetAutoRotate(bool enable)
    {
        autoRotate = enable;
    }

    /// <summary>
    /// 重置地球位置和缩放
    /// </summary>
    public void ResetEarth()
    {
        transform.rotation = Quaternion.identity;
        currentScale = 1.0f;
        transform.localScale = Vector3.one;
    }
}
