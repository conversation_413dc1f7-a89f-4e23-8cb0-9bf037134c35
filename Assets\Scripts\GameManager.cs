using UnityEngine;
using System.Collections;

/// <summary>
/// 游戏主管理器
/// 负责整个应用的生命周期管理和各模块协调
/// </summary>
public class GameManager : MonoBehaviour
{
    [Header("核心组件")]
    public ARManager arManager;
    public UIManager uiManager;
    public GeographyDataManager geographyDataManager;
    public TeachingModeManager teachingModeManager;
    public AudioManager audioManager;
    public GestureManager gestureManager;
    public DataLoader dataLoader;

    [Header("应用设置")]
    public bool enableDebugMode = false;
    public bool autoStartAR = true;
    public float splashScreenDuration = 2.0f;

    [Header("启动画面")]
    public GameObject splashScreen;
    public GameObject loadingScreen;

    // 单例模式
    private static GameManager instance;
    public static GameManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<GameManager>();
            }
            return instance;
        }
    }

    // 应用状态
    public enum AppState
    {
        Initializing,
        Loading,
        Ready,
        ARMode,
        TeachingMode,
        Settings
    }

    private AppState currentState = AppState.Initializing;
    private bool isDataLoaded = false;
    private bool isInitialized = false;

    void Awake()
    {
        // 单例模式设置
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeApplication();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        StartCoroutine(StartupSequence());
    }

    /// <summary>
    /// 初始化应用程序
    /// </summary>
    void InitializeApplication()
    {
        // 设置应用程序配置
        Application.targetFrameRate = 60;
        Screen.sleepTimeout = SleepTimeout.NeverSleep;
        
        // 设置日志级别
        if (enableDebugMode)
        {
            Debug.unityLogger.logEnabled = true;
        }

        Debug.Log("应用程序初始化完成");
    }

    /// <summary>
    /// 启动序列
    /// </summary>
    IEnumerator StartupSequence()
    {
        currentState = AppState.Initializing;

        // 显示启动画面
        if (splashScreen != null)
        {
            splashScreen.SetActive(true);
            yield return new WaitForSeconds(splashScreenDuration);
            splashScreen.SetActive(false);
        }

        // 显示加载画面
        if (loadingScreen != null)
        {
            loadingScreen.SetActive(true);
        }

        currentState = AppState.Loading;

        // 初始化各个管理器
        yield return StartCoroutine(InitializeManagers());

        // 加载数据
        yield return StartCoroutine(LoadApplicationData());

        // 隐藏加载画面
        if (loadingScreen != null)
        {
            loadingScreen.SetActive(false);
        }

        // 应用准备就绪
        currentState = AppState.Ready;
        OnApplicationReady();
    }

    /// <summary>
    /// 初始化各个管理器
    /// </summary>
    IEnumerator InitializeManagers()
    {
        Debug.Log("开始初始化管理器...");

        // 查找组件（如果没有手动分配）
        FindManagerComponents();

        // 初始化音频管理器
        if (audioManager != null)
        {
            // 音频管理器在Awake中自动初始化
            yield return new WaitForEndOfFrame();
        }

        // 初始化手势管理器
        if (gestureManager != null)
        {
            gestureManager.OnGestureDetected += HandleGestureInput;
        }

        // 初始化数据加载器
        if (dataLoader != null)
        {
            dataLoader.OnDataLoaded += OnDataLoadComplete;
        }

        Debug.Log("管理器初始化完成");
        yield return null;
    }

    /// <summary>
    /// 查找管理器组件
    /// </summary>
    void FindManagerComponents()
    {
        if (arManager == null)
            arManager = FindObjectOfType<ARManager>();
        
        if (uiManager == null)
            uiManager = FindObjectOfType<UIManager>();
        
        if (geographyDataManager == null)
            geographyDataManager = FindObjectOfType<GeographyDataManager>();
        
        if (teachingModeManager == null)
            teachingModeManager = FindObjectOfType<TeachingModeManager>();
        
        if (audioManager == null)
            audioManager = FindObjectOfType<AudioManager>();
        
        if (gestureManager == null)
            gestureManager = FindObjectOfType<GestureManager>();
        
        if (dataLoader == null)
            dataLoader = FindObjectOfType<DataLoader>();
    }

    /// <summary>
    /// 加载应用数据
    /// </summary>
    IEnumerator LoadApplicationData()
    {
        Debug.Log("开始加载应用数据...");

        // 等待数据加载完成
        if (dataLoader != null)
        {
            while (!dataLoader.IsDataReady())
            {
                yield return new WaitForSeconds(0.1f);
            }
        }

        // 加载用户设置
        LoadUserSettings();

        Debug.Log("应用数据加载完成");
    }

    /// <summary>
    /// 数据加载完成回调
    /// </summary>
    void OnDataLoadComplete()
    {
        isDataLoaded = true;
        Debug.Log("地理数据加载完成");
    }

    /// <summary>
    /// 应用准备就绪
    /// </summary>
    void OnApplicationReady()
    {
        isInitialized = true;
        Debug.Log("应用程序准备就绪");

        // 播放欢迎音效
        if (audioManager != null)
        {
            audioManager.PlayUISound("welcome");
        }

        // 如果启用自动AR模式，则启动AR
        if (autoStartAR)
        {
            StartARMode();
        }
    }

    /// <summary>
    /// 启动AR模式
    /// </summary>
    public void StartARMode()
    {
        if (!isInitialized) return;

        currentState = AppState.ARMode;
        
        if (arManager != null)
        {
            // AR管理器会自动初始化
        }

        if (uiManager != null)
        {
            uiManager.ShowARInstructions();
        }

        Debug.Log("AR模式已启动");
    }

    /// <summary>
    /// 启动教学模式
    /// </summary>
    public void StartTeachingMode()
    {
        if (!isInitialized) return;

        currentState = AppState.TeachingMode;
        
        if (teachingModeManager != null)
        {
            teachingModeManager.StartDemoMode();
        }

        Debug.Log("教学模式已启动");
    }

    /// <summary>
    /// 处理手势输入
    /// </summary>
    void HandleGestureInput(GestureEventData gestureData)
    {
        switch (currentState)
        {
            case AppState.ARMode:
                // AR模式下的手势处理由ARManager和EarthController处理
                break;
                
            case AppState.TeachingMode:
                // 教学模式下的手势处理
                break;
        }
    }

    /// <summary>
    /// 加载用户设置
    /// </summary>
    void LoadUserSettings()
    {
        // 加载音频设置
        if (audioManager != null)
        {
            // 音频管理器会自动加载设置
        }

        // 加载其他用户偏好设置
        // 例如：语言设置、显示设置等
    }

    /// <summary>
    /// 保存用户设置
    /// </summary>
    public void SaveUserSettings()
    {
        // 保存设置到PlayerPrefs
        PlayerPrefs.Save();
        Debug.Log("用户设置已保存");
    }

    /// <summary>
    /// 获取当前应用状态
    /// </summary>
    public AppState GetCurrentState()
    {
        return currentState;
    }

    /// <summary>
    /// 检查应用是否已初始化
    /// </summary>
    public bool IsInitialized()
    {
        return isInitialized;
    }

    /// <summary>
    /// 检查数据是否已加载
    /// </summary>
    public bool IsDataLoaded()
    {
        return isDataLoaded;
    }

    /// <summary>
    /// 退出应用
    /// </summary>
    public void QuitApplication()
    {
        Debug.Log("退出应用程序");
        
        // 保存用户设置
        SaveUserSettings();
        
        // 清理资源
        CleanupResources();
        
        // 退出应用
        Application.Quit();
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    void CleanupResources()
    {
        // 停止所有协程
        StopAllCoroutines();
        
        // 清理音频
        if (audioManager != null)
        {
            audioManager.StopSound(AudioType.Ambient);
        }
    }

    /// <summary>
    /// 应用暂停时调用
    /// </summary>
    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            // 应用暂停
            SaveUserSettings();
        }
        else
        {
            // 应用恢复
            if (audioManager != null && currentState == AppState.ARMode)
            {
                // 恢复背景音乐
            }
        }
    }

    /// <summary>
    /// 应用失去焦点时调用
    /// </summary>
    void OnApplicationFocus(bool hasFocus)
    {
        if (!hasFocus)
        {
            SaveUserSettings();
        }
    }
}
