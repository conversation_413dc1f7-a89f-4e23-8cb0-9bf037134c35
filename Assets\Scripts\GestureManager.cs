using UnityEngine;
using System.Collections;

/// <summary>
/// 手势类型枚举
/// </summary>
public enum GestureType
{
    None,
    Tap,
    DoubleTap,
    Drag,
    Pinch,
    Rotate
}

/// <summary>
/// 手势事件数据
/// </summary>
public class GestureEventData
{
    public GestureType gestureType;
    public Vector2 position;
    public Vector2 deltaPosition;
    public float deltaScale;
    public float deltaRotation;
    public float duration;
}

/// <summary>
/// 手势识别管理器
/// 负责识别和处理各种触摸手势
/// </summary>
public class GestureManager : MonoBehaviour
{
    [Header("手势设置")]
    public float tapTimeThreshold = 0.3f;
    public float doubleTapTimeThreshold = 0.5f;
    public float dragThreshold = 10f;
    public float pinchThreshold = 5f;
    public float rotationThreshold = 5f;

    [Header("调试")]
    public bool enableDebugLog = false;

    // 事件委托
    public System.Action<GestureEventData> OnGestureDetected;

    // 私有变量
    private Vector2 lastTouchPosition;
    private float lastTouchTime;
    private float lastTapTime;
    private bool isDragging = false;
    private bool isPinching = false;
    private bool isRotating = false;
    
    private float initialDistance;
    private float initialAngle;
    private Vector2 initialTouchCenter;

    void Update()
    {
        ProcessInput();
    }

    /// <summary>
    /// 处理输入
    /// </summary>
    void ProcessInput()
    {
        // 处理触摸输入
        if (Input.touchCount > 0)
        {
            ProcessTouchInput();
        }
        else
        {
            // 重置状态
            ResetGestureStates();
        }

        // 处理鼠标输入（用于编辑器测试）
        ProcessMouseInput();
    }

    /// <summary>
    /// 处理触摸输入
    /// </summary>
    void ProcessTouchInput()
    {
        if (Input.touchCount == 1)
        {
            ProcessSingleTouch();
        }
        else if (Input.touchCount == 2)
        {
            ProcessTwoFingerGestures();
        }
    }

    /// <summary>
    /// 处理单点触摸
    /// </summary>
    void ProcessSingleTouch()
    {
        Touch touch = Input.GetTouch(0);
        
        switch (touch.phase)
        {
            case TouchPhase.Began:
                OnTouchBegan(touch.position);
                break;
                
            case TouchPhase.Moved:
                OnTouchMoved(touch.position, touch.deltaPosition);
                break;
                
            case TouchPhase.Ended:
                OnTouchEnded(touch.position);
                break;
                
            case TouchPhase.Canceled:
                ResetGestureStates();
                break;
        }
    }

    /// <summary>
    /// 处理双指手势
    /// </summary>
    void ProcessTwoFingerGestures()
    {
        Touch touch1 = Input.GetTouch(0);
        Touch touch2 = Input.GetTouch(1);

        Vector2 touch1Pos = touch1.position;
        Vector2 touch2Pos = touch2.position;
        Vector2 touchCenter = (touch1Pos + touch2Pos) * 0.5f;

        if (touch1.phase == TouchPhase.Began || touch2.phase == TouchPhase.Began)
        {
            // 初始化双指手势
            initialDistance = Vector2.Distance(touch1Pos, touch2Pos);
            initialAngle = GetAngleBetweenTouches(touch1Pos, touch2Pos);
            initialTouchCenter = touchCenter;
            isPinching = false;
            isRotating = false;
        }
        else if (touch1.phase == TouchPhase.Moved || touch2.phase == TouchPhase.Moved)
        {
            float currentDistance = Vector2.Distance(touch1Pos, touch2Pos);
            float currentAngle = GetAngleBetweenTouches(touch1Pos, touch2Pos);

            // 检测缩放手势
            float deltaScale = currentDistance - initialDistance;
            if (Mathf.Abs(deltaScale) > pinchThreshold)
            {
                if (!isPinching)
                {
                    isPinching = true;
                    if (enableDebugLog) Debug.Log("Pinch gesture started");
                }

                GestureEventData gestureData = new GestureEventData
                {
                    gestureType = GestureType.Pinch,
                    position = touchCenter,
                    deltaScale = deltaScale / initialDistance
                };

                OnGestureDetected?.Invoke(gestureData);
            }

            // 检测旋转手势
            float deltaRotation = Mathf.DeltaAngle(initialAngle, currentAngle);
            if (Mathf.Abs(deltaRotation) > rotationThreshold)
            {
                if (!isRotating)
                {
                    isRotating = true;
                    if (enableDebugLog) Debug.Log("Rotation gesture started");
                }

                GestureEventData gestureData = new GestureEventData
                {
                    gestureType = GestureType.Rotate,
                    position = touchCenter,
                    deltaRotation = deltaRotation
                };

                OnGestureDetected?.Invoke(gestureData);
            }
        }
    }

    /// <summary>
    /// 触摸开始
    /// </summary>
    void OnTouchBegan(Vector2 position)
    {
        lastTouchPosition = position;
        lastTouchTime = Time.time;
        isDragging = false;
    }

    /// <summary>
    /// 触摸移动
    /// </summary>
    void OnTouchMoved(Vector2 position, Vector2 deltaPosition)
    {
        float distance = Vector2.Distance(position, lastTouchPosition);
        
        if (distance > dragThreshold && !isDragging)
        {
            isDragging = true;
            if (enableDebugLog) Debug.Log("Drag gesture started");
        }

        if (isDragging)
        {
            GestureEventData gestureData = new GestureEventData
            {
                gestureType = GestureType.Drag,
                position = position,
                deltaPosition = deltaPosition,
                duration = Time.time - lastTouchTime
            };

            OnGestureDetected?.Invoke(gestureData);
        }
    }

    /// <summary>
    /// 触摸结束
    /// </summary>
    void OnTouchEnded(Vector2 position)
    {
        float touchDuration = Time.time - lastTouchTime;
        
        if (!isDragging && touchDuration < tapTimeThreshold)
        {
            // 检测点击或双击
            float timeSinceLastTap = Time.time - lastTapTime;
            
            if (timeSinceLastTap < doubleTapTimeThreshold)
            {
                // 双击
                GestureEventData gestureData = new GestureEventData
                {
                    gestureType = GestureType.DoubleTap,
                    position = position
                };

                OnGestureDetected?.Invoke(gestureData);
                
                if (enableDebugLog) Debug.Log("Double tap detected");
                lastTapTime = 0f; // 重置以避免三击
            }
            else
            {
                // 单击
                StartCoroutine(DelayedTapDetection(position));
            }
            
            lastTapTime = Time.time;
        }

        ResetGestureStates();
    }

    /// <summary>
    /// 延迟点击检测（用于区分单击和双击）
    /// </summary>
    IEnumerator DelayedTapDetection(Vector2 position)
    {
        yield return new WaitForSeconds(doubleTapTimeThreshold);
        
        // 如果在等待期间没有第二次点击，则确认为单击
        if (Time.time - lastTapTime >= doubleTapTimeThreshold)
        {
            GestureEventData gestureData = new GestureEventData
            {
                gestureType = GestureType.Tap,
                position = position
            };

            OnGestureDetected?.Invoke(gestureData);
            
            if (enableDebugLog) Debug.Log("Tap detected");
        }
    }

    /// <summary>
    /// 处理鼠标输入（编辑器测试）
    /// </summary>
    void ProcessMouseInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            OnTouchBegan(Input.mousePosition);
        }
        else if (Input.GetMouseButton(0))
        {
            Vector2 currentMousePos = Input.mousePosition;
            Vector2 deltaPosition = currentMousePos - lastTouchPosition;
            OnTouchMoved(currentMousePos, deltaPosition);
        }
        else if (Input.GetMouseButtonUp(0))
        {
            OnTouchEnded(Input.mousePosition);
        }

        // 鼠标滚轮模拟缩放
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0)
        {
            GestureEventData gestureData = new GestureEventData
            {
                gestureType = GestureType.Pinch,
                position = Input.mousePosition,
                deltaScale = scroll
            };

            OnGestureDetected?.Invoke(gestureData);
        }
    }

    /// <summary>
    /// 获取两个触摸点之间的角度
    /// </summary>
    float GetAngleBetweenTouches(Vector2 touch1, Vector2 touch2)
    {
        Vector2 direction = touch2 - touch1;
        return Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
    }

    /// <summary>
    /// 重置手势状态
    /// </summary>
    void ResetGestureStates()
    {
        isDragging = false;
        isPinching = false;
        isRotating = false;
    }

    /// <summary>
    /// 启用/禁用手势识别
    /// </summary>
    public void SetGestureEnabled(bool enabled)
    {
        this.enabled = enabled;
        if (!enabled)
        {
            ResetGestureStates();
        }
    }

    /// <summary>
    /// 设置手势参数
    /// </summary>
    public void SetGestureParameters(float tapTime, float doubleTapTime, float dragThresh, float pinchThresh, float rotationThresh)
    {
        tapTimeThreshold = tapTime;
        doubleTapTimeThreshold = doubleTapTime;
        dragThreshold = dragThresh;
        pinchThreshold = pinchThresh;
        rotationThreshold = rotationThresh;
    }
}
