Shader "Custom/EarthShader"
{
    Properties
    {
        _MainTex ("Earth Texture", 2D) = "white" {}
        _NormalMap ("Normal Map", 2D) = "bump" {}
        _SpecularMap ("Specular Map", 2D) = "white" {}
        _EmissionMap ("Emission Map", 2D) = "black" {}
        _CloudTex ("Cloud Texture", 2D) = "white" {}
        
        _Glossiness ("Smoothness", Range(0,1)) = 0.5
        _Metallic ("Metallic", Range(0,1)) = 0.0
        _CloudSpeed ("Cloud Speed", Range(0,2)) = 0.1
        _CloudOpacity ("Cloud Opacity", Range(0,1)) = 0.5
        _AtmosphereColor ("Atmosphere Color", Color) = (0.5, 0.8, 1.0, 1.0)
        _AtmosphereThickness ("Atmosphere Thickness", Range(0,2)) = 1.0
    }
    
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 200

        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows
        #pragma target 3.0

        sampler2D _MainTex;
        sampler2D _NormalMap;
        sampler2D _SpecularMap;
        sampler2D _EmissionMap;
        sampler2D _CloudTex;

        struct Input
        {
            float2 uv_MainTex;
            float3 worldPos;
            float3 worldNormal;
            float3 viewDir;
        };

        half _Glossiness;
        half _Metallic;
        float _CloudSpeed;
        float _CloudOpacity;
        fixed4 _AtmosphereColor;
        float _AtmosphereThickness;

        void surf (Input IN, inout SurfaceOutputStandard o)
        {
            // 基础地球纹理
            fixed4 earthColor = tex2D(_MainTex, IN.uv_MainTex);
            
            // 云层纹理（带动画）
            float2 cloudUV = IN.uv_MainTex;
            cloudUV.x += _Time.y * _CloudSpeed;
            fixed4 cloudColor = tex2D(_CloudTex, cloudUV);
            
            // 混合地球和云层
            fixed4 finalColor = lerp(earthColor, cloudColor, cloudColor.a * _CloudOpacity);
            
            // 法线贴图
            fixed3 normal = UnpackNormal(tex2D(_NormalMap, IN.uv_MainTex));
            
            // 镜面反射贴图
            fixed4 specular = tex2D(_SpecularMap, IN.uv_MainTex);
            
            // 自发光贴图（夜晚城市灯光）
            fixed4 emission = tex2D(_EmissionMap, IN.uv_MainTex);
            
            // 计算大气层效果
            float fresnel = 1.0 - saturate(dot(normalize(IN.viewDir), normalize(IN.worldNormal)));
            fresnel = pow(fresnel, _AtmosphereThickness);
            
            // 输出
            o.Albedo = finalColor.rgb;
            o.Normal = normal;
            o.Metallic = _Metallic;
            o.Smoothness = _Glossiness * specular.r;
            o.Emission = emission.rgb + (_AtmosphereColor.rgb * fresnel * 0.5);
            o.Alpha = 1.0;
        }
        ENDCG
    }
    FallBack "Diffuse"
}
