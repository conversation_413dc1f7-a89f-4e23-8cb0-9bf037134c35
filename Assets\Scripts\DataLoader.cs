using UnityEngine;
using System.Collections.Generic;
using System.IO;

/// <summary>
/// 国家数据结构（用于JSON反序列化）
/// </summary>
[System.Serializable]
public class CountryData
{
    public string name;
    public string englishName;
    public string capital;
    public string continent;
    public long population;
    public float area;
    public string currency;
    public string[] languages;
    public string timeZone;
    public Coordinates coordinates;
    public string description;
    public string[] majorCities;
    public string climate;
    public string[] naturalResources;
}

/// <summary>
/// 城市数据结构（用于JSON反序列化）
/// </summary>
[System.Serializable]
public class CityData
{
    public string name;
    public string englishName;
    public string country;
    public string continent;
    public Coordinates coordinates;
    public long population;
    public float elevation;
    public string timeZone;
    public string climate;
    public string description;
    public string[] landmarks;
    public bool isCapital;
}

/// <summary>
/// 坐标数据结构
/// </summary>
[System.Serializable]
public class Coordinates
{
    public float latitude;
    public float longitude;
}

/// <summary>
/// 国家数据容器
/// </summary>
[System.Serializable]
public class CountriesContainer
{
    public CountryData[] countries;
}

/// <summary>
/// 城市数据容器
/// </summary>
[System.Serializable]
public class CitiesContainer
{
    public CityData[] cities;
}

/// <summary>
/// 数据加载器
/// 负责从JSON文件加载地理数据
/// </summary>
public class DataLoader : MonoBehaviour
{
    [Header("数据文件路径")]
    public string countriesDataPath = "Data/CountriesData";
    public string citiesDataPath = "Data/CitiesData";

    [Header("加载状态")]
    public bool isDataLoaded = false;

    private Dictionary<string, CountryData> countriesDict;
    private Dictionary<string, CityData> citiesDict;
    private List<CountryData> countriesList;
    private List<CityData> citiesList;

    // 事件
    public System.Action OnDataLoaded;

    void Start()
    {
        LoadAllData();
    }

    /// <summary>
    /// 加载所有数据
    /// </summary>
    public void LoadAllData()
    {
        StartCoroutine(LoadDataCoroutine());
    }

    /// <summary>
    /// 数据加载协程
    /// </summary>
    System.Collections.IEnumerator LoadDataCoroutine()
    {
        Debug.Log("开始加载地理数据...");

        // 初始化容器
        countriesDict = new Dictionary<string, CountryData>();
        citiesDict = new Dictionary<string, CityData>();
        countriesList = new List<CountryData>();
        citiesList = new List<CityData>();

        // 加载国家数据
        yield return StartCoroutine(LoadCountriesData());

        // 加载城市数据
        yield return StartCoroutine(LoadCitiesData());

        // 标记数据加载完成
        isDataLoaded = true;
        OnDataLoaded?.Invoke();

        Debug.Log($"地理数据加载完成 - 国家: {countriesList.Count}, 城市: {citiesList.Count}");
    }

    /// <summary>
    /// 加载国家数据
    /// </summary>
    System.Collections.IEnumerator LoadCountriesData()
    {
        TextAsset countriesJson = Resources.Load<TextAsset>(countriesDataPath);
        
        if (countriesJson != null)
        {
            try
            {
                CountriesContainer container = JsonUtility.FromJson<CountriesContainer>(countriesJson.text);
                
                if (container != null && container.countries != null)
                {
                    foreach (var country in container.countries)
                    {
                        countriesList.Add(country);
                        
                        if (!countriesDict.ContainsKey(country.name))
                        {
                            countriesDict.Add(country.name, country);
                        }
                        
                        // 也添加英文名称索引
                        if (!string.IsNullOrEmpty(country.englishName) && 
                            !countriesDict.ContainsKey(country.englishName))
                        {
                            countriesDict.Add(country.englishName, country);
                        }
                    }
                    
                    Debug.Log($"成功加载 {container.countries.Length} 个国家数据");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"解析国家数据时出错: {e.Message}");
            }
        }
        else
        {
            Debug.LogWarning($"未找到国家数据文件: {countriesDataPath}");
        }

        yield return null;
    }

    /// <summary>
    /// 加载城市数据
    /// </summary>
    System.Collections.IEnumerator LoadCitiesData()
    {
        TextAsset citiesJson = Resources.Load<TextAsset>(citiesDataPath);
        
        if (citiesJson != null)
        {
            try
            {
                CitiesContainer container = JsonUtility.FromJson<CitiesContainer>(citiesJson.text);
                
                if (container != null && container.cities != null)
                {
                    foreach (var city in container.cities)
                    {
                        citiesList.Add(city);
                        
                        if (!citiesDict.ContainsKey(city.name))
                        {
                            citiesDict.Add(city.name, city);
                        }
                        
                        // 也添加英文名称索引
                        if (!string.IsNullOrEmpty(city.englishName) && 
                            !citiesDict.ContainsKey(city.englishName))
                        {
                            citiesDict.Add(city.englishName, city);
                        }
                    }
                    
                    Debug.Log($"成功加载 {container.cities.Length} 个城市数据");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"解析城市数据时出错: {e.Message}");
            }
        }
        else
        {
            Debug.LogWarning($"未找到城市数据文件: {citiesDataPath}");
        }

        yield return null;
    }

    /// <summary>
    /// 获取国家数据
    /// </summary>
    public CountryData GetCountryData(string countryName)
    {
        if (countriesDict != null && countriesDict.ContainsKey(countryName))
        {
            return countriesDict[countryName];
        }
        return null;
    }

    /// <summary>
    /// 获取城市数据
    /// </summary>
    public CityData GetCityData(string cityName)
    {
        if (citiesDict != null && citiesDict.ContainsKey(cityName))
        {
            return citiesDict[cityName];
        }
        return null;
    }

    /// <summary>
    /// 根据坐标查找最近的城市
    /// </summary>
    public CityData FindNearestCity(float latitude, float longitude, float maxDistance = 500f)
    {
        if (citiesList == null) return null;

        CityData nearestCity = null;
        float minDistance = float.MaxValue;

        foreach (var city in citiesList)
        {
            float distance = CalculateDistance(latitude, longitude, 
                city.coordinates.latitude, city.coordinates.longitude);
            
            if (distance < minDistance && distance <= maxDistance)
            {
                minDistance = distance;
                nearestCity = city;
            }
        }

        return nearestCity;
    }

    /// <summary>
    /// 根据坐标查找最近的国家
    /// </summary>
    public CountryData FindNearestCountry(float latitude, float longitude)
    {
        if (countriesList == null) return null;

        CountryData nearestCountry = null;
        float minDistance = float.MaxValue;

        foreach (var country in countriesList)
        {
            float distance = CalculateDistance(latitude, longitude, 
                country.coordinates.latitude, country.coordinates.longitude);
            
            if (distance < minDistance)
            {
                minDistance = distance;
                nearestCountry = country;
            }
        }

        return nearestCountry;
    }

    /// <summary>
    /// 搜索国家
    /// </summary>
    public List<CountryData> SearchCountries(string searchTerm)
    {
        List<CountryData> results = new List<CountryData>();
        
        if (countriesList == null || string.IsNullOrEmpty(searchTerm)) return results;

        string lowerSearchTerm = searchTerm.ToLower();

        foreach (var country in countriesList)
        {
            if (country.name.ToLower().Contains(lowerSearchTerm) ||
                country.englishName.ToLower().Contains(lowerSearchTerm) ||
                country.capital.ToLower().Contains(lowerSearchTerm))
            {
                results.Add(country);
            }
        }

        return results;
    }

    /// <summary>
    /// 搜索城市
    /// </summary>
    public List<CityData> SearchCities(string searchTerm)
    {
        List<CityData> results = new List<CityData>();
        
        if (citiesList == null || string.IsNullOrEmpty(searchTerm)) return results;

        string lowerSearchTerm = searchTerm.ToLower();

        foreach (var city in citiesList)
        {
            if (city.name.ToLower().Contains(lowerSearchTerm) ||
                city.englishName.ToLower().Contains(lowerSearchTerm) ||
                city.country.ToLower().Contains(lowerSearchTerm))
            {
                results.Add(city);
            }
        }

        return results;
    }

    /// <summary>
    /// 获取所有国家列表
    /// </summary>
    public List<CountryData> GetAllCountries()
    {
        return countriesList ?? new List<CountryData>();
    }

    /// <summary>
    /// 获取所有城市列表
    /// </summary>
    public List<CityData> GetAllCities()
    {
        return citiesList ?? new List<CityData>();
    }

    /// <summary>
    /// 根据大洲获取国家
    /// </summary>
    public List<CountryData> GetCountriesByContinent(string continent)
    {
        List<CountryData> results = new List<CountryData>();
        
        if (countriesList == null) return results;

        foreach (var country in countriesList)
        {
            if (country.continent.Equals(continent, System.StringComparison.OrdinalIgnoreCase))
            {
                results.Add(country);
            }
        }

        return results;
    }

    /// <summary>
    /// 计算两点间距离（公里）
    /// </summary>
    float CalculateDistance(float lat1, float lon1, float lat2, float lon2)
    {
        const float R = 6371f; // 地球半径（公里）
        
        float dLat = (lat2 - lat1) * Mathf.Deg2Rad;
        float dLon = (lon2 - lon1) * Mathf.Deg2Rad;
        
        float a = Mathf.Sin(dLat / 2) * Mathf.Sin(dLat / 2) +
                  Mathf.Cos(lat1 * Mathf.Deg2Rad) * Mathf.Cos(lat2 * Mathf.Deg2Rad) *
                  Mathf.Sin(dLon / 2) * Mathf.Sin(dLon / 2);
        
        float c = 2 * Mathf.Atan2(Mathf.Sqrt(a), Mathf.Sqrt(1 - a));
        
        return R * c;
    }

    /// <summary>
    /// 检查数据是否已加载
    /// </summary>
    public bool IsDataReady()
    {
        return isDataLoaded;
    }
}
