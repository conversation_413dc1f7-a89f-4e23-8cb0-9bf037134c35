# AR地球仪教育软件 - 开发者文档

## 项目概述

AR地球仪教育软件是一个基于Unity引擎开发的跨平台AR教育应用，支持Android和Windows平台。

## 技术架构

### 核心技术栈
- **游戏引擎**: Unity 2022.3 LTS
- **AR框架**: AR Foundation 5.0.7
- **编程语言**: C#
- **渲染管线**: Universal Render Pipeline (URP)
- **UI框架**: Unity UI (uGUI) + TextMeshPro

### 平台支持
- **Android**: ARCore (API Level 24+)
- **Windows**: Windows Mixed Reality (可选)
- **iOS**: ARKit (未来支持)

## 项目结构

```
ARGlobe/
├── Assets/
│   ├── Scripts/              # C# 脚本
│   │   ├── ARManager.cs      # AR核心管理
│   │   ├── EarthController.cs # 地球控制
│   │   ├── UIManager.cs      # UI管理
│   │   ├── GeographyDataManager.cs # 地理数据
│   │   ├── TeachingModeManager.cs # 教学模式
│   │   ├── GestureManager.cs # 手势识别
│   │   ├── AudioManager.cs   # 音频管理
│   │   └── DataLoader.cs     # 数据加载
│   ├── Materials/            # 材质文件
│   │   ├── EarthShader.shader # 地球着色器
│   │   └── AtmosphereShader.shader # 大气层着色器
│   ├── Textures/             # 纹理贴图
│   ├── Models/               # 3D模型
│   ├── Prefabs/              # 预制体
│   ├── Scenes/               # 场景文件
│   ├── UI/                   # UI资源
│   ├── Data/                 # 地理数据
│   │   ├── CountriesData.json
│   │   └── CitiesData.json
│   └── Resources/            # 运行时资源
├── Packages/                 # 包管理
├── ProjectSettings/          # 项目设置
└── Documentation/            # 文档
```

## 核心模块详解

### 1. AR管理器 (ARManager)

负责AR会话管理、平面检测、地球模型放置等核心AR功能。

**主要功能**:
- AR会话初始化
- 平面检测和可视化
- 地球模型放置
- 触摸输入处理

**关键方法**:
```csharp
void InitializeAR()           // 初始化AR系统
void TryPlaceEarth()         // 尝试放置地球
void HandleTouchInput()      // 处理触摸输入
void ResetARSession()        // 重置AR会话
```

### 2. 地球控制器 (EarthController)

管理地球模型的旋转、缩放、交互等功能。

**主要功能**:
- 地球自动旋转
- 手势交互处理
- 昼夜循环效果
- 材质和光照管理

**关键方法**:
```csharp
void AutoRotateEarth()       // 自动旋转
void HandleInput()           // 处理输入
void ScaleEarth()           // 缩放地球
void OnEarthTouched()       // 触摸回调
```

### 3. 地理数据管理器 (GeographyDataManager)

管理地理信息数据，提供位置查询和搜索功能。

**数据结构**:
```csharp
public class LocationInfo
{
    public string countryName;
    public string cityName;
    public float latitude;
    public float longitude;
    public long population;
    public string climate;
    // ... 其他属性
}
```

**关键方法**:
```csharp
LocationInfo GetLocationInfo(float lat, float lon)
List<LocationInfo> SearchLocations(string term)
float CalculateDistance(float lat1, float lon1, float lat2, float lon2)
```

### 4. UI管理器 (UIManager)

管理所有用户界面元素和交互。

**主要功能**:
- 信息面板显示
- 搜索界面管理
- 设置面板控制
- 教学模式UI

### 5. 教学模式管理器 (TeachingModeManager)

实现教育功能，包括演示模式和测验系统。

**功能模块**:
- 地理知识演示
- 互动问答系统
- 学习进度跟踪
- 知识点标记

## 开发环境设置

### 必需软件
1. **Unity Hub** (最新版本)
2. **Unity 2022.3 LTS**
3. **Visual Studio 2022** 或 **Visual Studio Code**
4. **Android SDK** (用于Android构建)

### Unity包依赖
```json
{
  "com.unity.xr.arfoundation": "5.0.7",
  "com.unity.xr.arcore": "5.0.7",
  "com.unity.xr.management": "4.4.0",
  "com.unity.render-pipelines.universal": "14.0.8",
  "com.unity.textmeshpro": "3.0.6"
}
```

### 项目配置

1. **导入项目**
   ```bash
   # 克隆项目
   git clone <repository-url>
   
   # 在Unity Hub中添加项目
   # 选择Unity 2022.3 LTS版本
   ```

2. **配置AR Foundation**
   - 打开 XR Plug-in Management 设置
   - 启用 ARCore (Android) 或 Windows Mixed Reality
   - 配置AR Foundation 组件

3. **构建设置**
   ```csharp
   // 使用编辑器菜单
   Build -> Setup Project        // 初始化项目设置
   Build -> Build Android       // 构建Android版本
   Build -> Build Windows       // 构建Windows版本
   ```

## 数据管理

### 地理数据格式

项目使用JSON格式存储地理数据：

```json
{
  "countries": [
    {
      "name": "中国",
      "englishName": "China",
      "capital": "北京",
      "coordinates": {
        "latitude": 35.0,
        "longitude": 104.0
      },
      "population": 1400000000,
      "description": "..."
    }
  ]
}
```

### 数据加载流程

1. **启动时加载**: DataLoader 在启动时从 Resources 文件夹加载数据
2. **数据缓存**: 将JSON数据解析为C#对象并缓存在内存中
3. **查询优化**: 使用Dictionary进行快速查找

## 性能优化

### 渲染优化
- 使用LOD系统减少远距离模型复杂度
- 实现视锥体剔除
- 优化纹理分辨率和压缩格式

### 内存管理
- 及时释放不用的资源
- 使用对象池管理频繁创建的对象
- 压缩音频和纹理资源

### AR性能
- 限制同时追踪的平面数量
- 优化光线投射频率
- 减少不必要的AR更新

## 测试指南

### 单元测试
```csharp
[Test]
public void TestDistanceCalculation()
{
    float distance = GeographyDataManager.CalculateDistance(0, 0, 1, 1);
    Assert.AreEqual(157.2f, distance, 0.1f);
}
```

### AR测试
1. **设备测试**: 在支持ARCore的真实设备上测试
2. **环境测试**: 测试不同光照和表面条件
3. **性能测试**: 监控帧率和内存使用

### 用户测试
- 可用性测试
- 教育效果评估
- 跨平台兼容性测试

## 构建和部署

### Android构建
```csharp
// 自动构建脚本
BuildScript.BuildAndroid();

// 手动构建步骤
// 1. 切换到Android平台
// 2. 配置Player Settings
// 3. 设置Keystore (发布版本)
// 4. 构建APK
```

### Windows构建
```csharp
// 自动构建脚本
BuildScript.BuildWindows();

// 手动构建步骤
// 1. 切换到Windows平台
// 2. 配置Player Settings
// 3. 构建可执行文件
```

## 扩展开发

### 添加新的地理数据
1. 更新JSON数据文件
2. 扩展数据结构类
3. 更新UI显示逻辑

### 添加新的教学内容
1. 在TeachingModeManager中添加新主题
2. 创建相应的演示逻辑
3. 添加测验问题

### 支持新平台
1. 配置相应的XR插件
2. 适配平台特定的输入系统
3. 测试和优化性能

## 常见问题

### 开发问题
1. **AR Foundation版本兼容性**: 确保使用匹配的AR Foundation和ARCore版本
2. **Android权限**: 确保在AndroidManifest.xml中声明摄像头权限
3. **纹理压缩**: 不同平台使用不同的纹理压缩格式

### 性能问题
1. **帧率下降**: 检查Draw Call数量和纹理内存使用
2. **内存泄漏**: 使用Unity Profiler检查内存分配
3. **AR追踪丢失**: 优化环境光照和表面纹理

## 版本控制

### Git工作流
```bash
# 功能开发
git checkout -b feature/new-feature
git commit -m "Add new feature"
git push origin feature/new-feature

# 代码审查和合并
# 创建Pull Request
# 代码审查通过后合并到main分支
```

### 版本标记
```bash
# 发布新版本
git tag -a v1.0.0 -m "Release version 1.0.0"
git push origin v1.0.0
```

## 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 等待代码审查

## 许可证

本项目采用MIT许可证，详见LICENSE文件。
