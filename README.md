# AR地球仪教育软件

一款基于AR技术的交互式地球仪教育软件，帮助学生和地理爱好者更好地学习和理解地球知识。

## 功能特性

### 🌍 核心功能
- **AR地球仪**: 使用增强现实技术在真实环境中显示3D地球模型
- **交互操作**: 支持手势控制，可以旋转、缩放、移动地球
- **地理信息**: 详细的国家、城市、地形、气候等地理数据
- **教学模式**: 专为教育设计的演示和学习功能

### 📚 教育内容
- 国家和地区详细信息
- 地理坐标和经纬度系统
- 气候带和时区可视化
- 地形高度和海洋深度
- 人口、经济、文化数据

### 🎯 交互功能
- 点击查看地区详情
- 搜索国家和城市
- 测量距离和面积
- 3D地形可视化
- 实时天气信息（可选）

## 技术架构

- **引擎**: Unity 2022.3 LTS
- **AR框架**: AR Foundation
- **平台支持**: Android, Windows
- **编程语言**: C#

## 项目结构

```
ARGlobe/
├── Assets/
│   ├── Scripts/           # C# 脚本
│   ├── Materials/         # 材质文件
│   ├── Textures/          # 纹理贴图
│   ├── Models/            # 3D模型
│   ├── Prefabs/           # 预制体
│   ├── Scenes/            # 场景文件
│   ├── UI/                # 用户界面
│   └── Data/              # 地理数据
├── Packages/              # 包管理
├── ProjectSettings/       # 项目设置
└── Documentation/         # 文档
```

## 安装和运行

### 系统要求
- **Android**: Android 7.0+ (API Level 24+), ARCore支持
- **Windows**: Windows 10+, 支持Windows Mixed Reality (可选)
- **开发环境**: Unity 2022.3 LTS, Visual Studio 2022

### 开发设置
1. 安装Unity Hub和Unity 2022.3 LTS
2. 克隆或下载项目
3. 在Unity Hub中打开项目
4. 安装AR Foundation包
5. 配置目标平台设置

## 使用说明

### AR模式
1. 启动应用
2. 将设备摄像头对准平坦表面
3. 等待平面检测完成
4. 点击屏幕放置地球模型
5. 使用手势进行交互

### 学习功能
- 点击地球上的任意位置查看详细信息
- 使用搜索功能快速定位
- 切换不同的显示模式（地形、气候、政治等）
- 参与互动问答测试

## 开发计划

- [x] 项目架构设计
- [ ] Unity项目基础搭建
- [ ] 3D地球模型实现
- [ ] AR交互功能开发
- [ ] 地理数据集成
- [ ] 用户界面设计
- [ ] 教学模式实现
- [ ] 性能优化和测试

## 贡献指南

欢迎提交问题报告和功能建议！

## 许可证

本项目采用MIT许可证。
