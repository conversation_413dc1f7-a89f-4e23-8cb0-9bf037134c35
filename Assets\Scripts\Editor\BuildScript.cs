using UnityEngine;
using UnityEditor;
using System.IO;

/// <summary>
/// 构建脚本
/// 用于自动化构建Android和Windows版本
/// </summary>
public class BuildScript
{
    private static readonly string[] scenes = {
        "Assets/Scenes/MainScene.unity"
    };

    [MenuItem("Build/Build Android")]
    public static void BuildAndroid()
    {
        string buildPath = "Builds/Android/ARGlobe.apk";
        
        // 确保构建目录存在
        Directory.CreateDirectory(Path.GetDirectoryName(buildPath));
        
        // 设置Android构建设置
        EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Android, BuildTarget.Android);
        
        // 配置Android设置
        PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel24;
        PlayerSettings.Android.targetSdkVersion = AndroidSdkVersions.AndroidApiLevelAuto;
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, "com.arglobe.education");
        PlayerSettings.productName = "AR地球仪教育软件";
        PlayerSettings.companyName = "ARGlobeEducation";
        
        // 启用AR支持
        XRSettings.enabled = true;
        
        BuildPipeline.BuildPlayer(scenes, buildPath, BuildTarget.Android, BuildOptions.None);
        
        Debug.Log($"Android构建完成: {buildPath}");
    }

    [MenuItem("Build/Build Windows")]
    public static void BuildWindows()
    {
        string buildPath = "Builds/Windows/ARGlobe.exe";
        
        // 确保构建目录存在
        Directory.CreateDirectory(Path.GetDirectoryName(buildPath));
        
        // 设置Windows构建设置
        EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Standalone, BuildTarget.StandaloneWindows64);
        
        // 配置Windows设置
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.arglobe.education");
        PlayerSettings.productName = "AR地球仪教育软件";
        PlayerSettings.companyName = "ARGlobeEducation";
        
        BuildPipeline.BuildPlayer(scenes, buildPath, BuildTarget.StandaloneWindows64, BuildOptions.None);
        
        Debug.Log($"Windows构建完成: {buildPath}");
    }

    [MenuItem("Build/Build All Platforms")]
    public static void BuildAllPlatforms()
    {
        BuildAndroid();
        BuildWindows();
        Debug.Log("所有平台构建完成！");
    }

    [MenuItem("Build/Setup Project")]
    public static void SetupProject()
    {
        // 创建必要的文件夹
        CreateFolderIfNotExists("Assets/Resources");
        CreateFolderIfNotExists("Assets/Resources/Data");
        CreateFolderIfNotExists("Assets/StreamingAssets");
        CreateFolderIfNotExists("Builds");
        CreateFolderIfNotExists("Builds/Android");
        CreateFolderIfNotExists("Builds/Windows");

        // 移动数据文件到Resources文件夹
        MoveDataFiles();

        // 配置项目设置
        ConfigureProjectSettings();

        Debug.Log("项目设置完成！");
    }

    private static void CreateFolderIfNotExists(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
            AssetDatabase.Refresh();
        }
    }

    private static void MoveDataFiles()
    {
        // 移动JSON数据文件到Resources文件夹
        string[] dataFiles = {
            "Assets/Data/CountriesData.json",
            "Assets/Data/CitiesData.json"
        };

        foreach (string file in dataFiles)
        {
            if (File.Exists(file))
            {
                string fileName = Path.GetFileName(file);
                string targetPath = $"Assets/Resources/Data/{fileName}";
                
                if (!File.Exists(targetPath))
                {
                    File.Copy(file, targetPath);
                    AssetDatabase.Refresh();
                    Debug.Log($"已复制 {fileName} 到 Resources/Data/");
                }
            }
        }
    }

    private static void ConfigureProjectSettings()
    {
        // 配置基本设置
        PlayerSettings.productName = "AR地球仪教育软件";
        PlayerSettings.companyName = "ARGlobeEducation";
        PlayerSettings.bundleVersion = "1.0.0";

        // 配置Android设置
        PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel24;
        PlayerSettings.Android.targetSdkVersion = AndroidSdkVersions.AndroidApiLevelAuto;
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, "com.arglobe.education");

        // 配置Windows设置
        PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Standalone, "com.arglobe.education");

        // 配置图形设置
        PlayerSettings.colorSpace = ColorSpace.Linear;
        PlayerSettings.SetGraphicsAPIs(BuildTarget.Android, new UnityEngine.Rendering.GraphicsDeviceType[] {
            UnityEngine.Rendering.GraphicsDeviceType.OpenGLES3,
            UnityEngine.Rendering.GraphicsDeviceType.Vulkan
        });

        // 配置XR设置
        PlayerSettings.virtualRealitySupported = false; // AR不需要VR支持

        Debug.Log("项目设置已配置");
    }

    [MenuItem("Build/Clean Build Folders")]
    public static void CleanBuildFolders()
    {
        string[] buildPaths = {
            "Builds/Android",
            "Builds/Windows"
        };

        foreach (string path in buildPaths)
        {
            if (Directory.Exists(path))
            {
                Directory.Delete(path, true);
                Debug.Log($"已清理构建文件夹: {path}");
            }
        }

        Debug.Log("构建文件夹清理完成！");
    }

    [MenuItem("Build/Open Build Folder")]
    public static void OpenBuildFolder()
    {
        string buildPath = Path.GetFullPath("Builds");
        if (Directory.Exists(buildPath))
        {
            EditorUtility.RevealInFinder(buildPath);
        }
        else
        {
            Debug.LogWarning("构建文件夹不存在！");
        }
    }
}
