# AR地球仪教育软件 - 快速开始指南

## 🌍 项目简介

AR地球仪教育软件是一款创新的增强现实教育应用，运用AR科技让学生和地理爱好者更直观地学习地球知识。通过手机或平板电脑，用户可以在现实环境中放置一个3D地球模型，并与之进行交互学习。

## ✨ 主要特性

### 🎯 核心功能
- **AR地球仪**: 在现实环境中显示3D地球模型
- **交互操作**: 支持旋转、缩放、点击查看详情
- **地理信息**: 详细的国家、城市、气候、时区数据
- **搜索功能**: 快速定位任意国家或城市
- **教学模式**: 专业的地理知识演示和测验

### 📚 教育内容
- 🌏 世界各国详细信息
- 🏙️ 主要城市和地标
- 🌡️ 气候带分布
- ⏰ 全球时区系统
- 🏔️ 地形和地貌特征
- 👥 人口分布数据

### 🎮 交互功能
- 👆 点击查看地区详情
- 🔍 智能搜索系统
- 📏 距离测量工具
- 🎓 互动学习模式
- 🧪 地理知识测验

## 🚀 快速开始

### 系统要求
- **Android**: 7.0+ (支持ARCore)
- **Windows**: 10+ (可选AR支持)
- **RAM**: 3GB+ (Android) / 4GB+ (Windows)
- **存储**: 500MB+ 可用空间

### 安装步骤

#### 开发环境搭建
1. **安装Unity**
   - 下载Unity Hub
   - 安装Unity 2022.3 LTS
   - 添加Android Build Support模块

2. **克隆项目**
   ```bash
   git clone <项目地址>
   cd ar-globe-education
   ```

3. **打开项目**
   - 启动Unity Hub
   - 点击"添加"选择项目文件夹
   - 使用Unity 2022.3 LTS打开

4. **配置AR Foundation**
   - 打开Window > XR > XR Plug-in Management
   - 在Android标签下启用ARCore
   - 在Windows标签下启用Windows Mixed Reality (可选)

### 构建应用

#### Android版本
```bash
# 在Unity编辑器中
1. File > Build Settings
2. 选择Android平台
3. 点击"Switch Platform"
4. 配置Player Settings
5. 点击"Build"生成APK
```

#### Windows版本
```bash
# 在Unity编辑器中
1. File > Build Settings
2. 选择PC, Mac & Linux Standalone
3. 点击"Switch Platform"
4. 点击"Build"生成可执行文件
```

#### 使用自动构建脚本
```bash
# 在Unity编辑器中
Build > Setup Project      # 初始化项目设置
Build > Build Android      # 构建Android版本
Build > Build Windows      # 构建Windows版本
Build > Build All Platforms # 构建所有平台
```

## 📱 使用教程

### 首次启动
1. 启动应用后会显示欢迎界面
2. 允许摄像头权限（AR功能必需）
3. 阅读使用说明

### AR模式使用
1. **环境准备**
   - 确保光线充足
   - 找一个平坦的表面
   - 保持设备稳定

2. **放置地球**
   - 将摄像头对准平面
   - 等待平面检测完成
   - 点击屏幕放置地球模型

3. **交互操作**
   - 单指拖拽：旋转地球
   - 双指捏合：缩放地球
   - 点击地球：查看地理信息

### 教学功能
1. **演示模式**
   - 选择地理主题
   - 观看自动演示
   - 学习专业知识

2. **测验模式**
   - 参与地理问答
   - 查看答题结果
   - 获得详细解释

## 🛠️ 开发指南

### 项目结构
```
ARGlobe/
├── Assets/
│   ├── Scripts/           # C# 脚本
│   ├── Materials/         # 材质和着色器
│   ├── Textures/          # 纹理贴图
│   ├── Models/            # 3D模型
│   ├── Prefabs/           # 预制体
│   ├── Scenes/            # 场景文件
│   ├── UI/                # 用户界面
│   └── Data/              # 地理数据
├── Documentation/         # 项目文档
└── README.md             # 项目说明
```

### 核心脚本说明

| 脚本名称 | 功能描述 |
|---------|---------|
| `GameManager.cs` | 应用主管理器，协调各模块 |
| `ARManager.cs` | AR核心功能管理 |
| `EarthController.cs` | 地球模型控制 |
| `UIManager.cs` | 用户界面管理 |
| `GeographyDataManager.cs` | 地理数据管理 |
| `TeachingModeManager.cs` | 教学模式管理 |
| `GestureManager.cs` | 手势识别处理 |
| `AudioManager.cs` | 音效管理 |
| `DataLoader.cs` | 数据加载器 |

### 添加新功能

1. **添加新的地理数据**
   ```json
   // 编辑 Assets/Data/CountriesData.json
   {
     "name": "新国家",
     "capital": "首都",
     "coordinates": {"latitude": 0, "longitude": 0},
     "description": "描述信息"
   }
   ```

2. **添加新的教学内容**
   ```csharp
   // 在 TeachingModeManager.cs 中添加
   demoTopics.Add("新主题");
   ```

3. **自定义UI界面**
   - 编辑UI预制体
   - 修改UIManager.cs中的逻辑

## 🔧 故障排除

### 常见问题

1. **AR功能无法使用**
   - 检查设备ARCore支持
   - 确认摄像头权限
   - 改善环境光照

2. **应用运行缓慢**
   - 关闭后台应用
   - 降低图形质量
   - 检查设备性能

3. **构建失败**
   - 检查Unity版本
   - 确认包依赖完整
   - 查看控制台错误信息

### 性能优化建议

1. **设备要求**
   - 使用性能较好的设备
   - 确保充足的存储空间
   - 保持设备电量充足

2. **应用设置**
   - 适当调整图形质量
   - 关闭不必要的特效
   - 优化纹理分辨率

## 📖 更多资源

- 📚 [详细使用说明](Documentation/使用说明.md)
- 👨‍💻 [开发者文档](Documentation/开发者文档.md)
- 🐛 [问题反馈](https://github.com/your-repo/issues)
- 💬 [讨论社区](https://github.com/your-repo/discussions)

## 🤝 贡献指南

我们欢迎社区贡献！请查看贡献指南了解如何参与项目开发。

1. Fork 项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**开始您的AR地球探索之旅吧！** 🌍✨
