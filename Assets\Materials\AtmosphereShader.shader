Shader "Custom/AtmosphereShader"
{
    Properties
    {
        _AtmosphereColor ("Atmosphere Color", Color) = (0.5, 0.8, 1.0, 1.0)
        _Intensity ("Intensity", Range(0, 5)) = 2.0
        _Falloff ("Falloff", Range(0, 10)) = 5.0
        _Transparency ("Transparency", Range(0, 1)) = 0.8
    }
    
    SubShader
    {
        Tags { 
            "Queue"="Transparent" 
            "RenderType"="Transparent" 
            "IgnoreProjector"="True"
        }
        
        Blend SrcAlpha OneMinusSrcAlpha
        ZWrite Off
        Cull Front
        
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                float3 normal : NORMAL;
                float3 viewDir : TEXCOORD0;
                float4 worldPos : TEXCOORD1;
            };

            fixed4 _AtmosphereColor;
            float _Intensity;
            float _Falloff;
            float _Transparency;

            v2f vert (appdata v)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.normal = UnityObjectToWorldNormal(v.normal);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex);
                o.viewDir = normalize(_WorldSpaceCameraPos - o.worldPos.xyz);
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                // 计算菲涅尔效应
                float fresnel = 1.0 - saturate(dot(normalize(i.viewDir), normalize(i.normal)));
                fresnel = pow(fresnel, _Falloff);
                
                // 应用强度和透明度
                float alpha = fresnel * _Intensity * _Transparency;
                
                return fixed4(_AtmosphereColor.rgb, alpha);
            }
            ENDCG
        }
    }
    FallBack "Transparent/Diffuse"
}
