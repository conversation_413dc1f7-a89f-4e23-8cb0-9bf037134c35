using UnityEngine;
using UnityEngine.XR.ARFoundation;
using UnityEngine.XR.ARSubsystems;
using System.Collections.Generic;

/// <summary>
/// AR地球仪的核心管理器
/// 负责AR会话管理、平面检测、地球模型放置等核心功能
/// </summary>
public class ARManager : MonoBehaviour
{
    [Header("AR组件")]
    public ARSessionOrigin arSessionOrigin;
    public ARSession arSession;
    public ARPlaneManager arPlaneManager;
    public ARRaycastManager arRaycastManager;
    public Camera arCamera;

    [Header("地球模型")]
    public GameObject earthPrefab;
    public Transform earthContainer;

    [Header("UI组件")]
    public GameObject planeDetectionUI;
    public GameObject instructionUI;

    private GameObject currentEarthInstance;
    private bool isEarthPlaced = false;
    private List<ARRaycastHit> raycastHits = new List<ARRaycastHit>();

    void Start()
    {
        InitializeAR();
    }

    void Update()
    {
        HandleTouchInput();
        UpdateUI();
    }

    /// <summary>
    /// 初始化AR系统
    /// </summary>
    void InitializeAR()
    {
        if (arSession == null)
        {
            Debug.LogError("AR Session未设置！");
            return;
        }

        // 启用平面检测
        if (arPlaneManager != null)
        {
            arPlaneManager.enabled = true;
            arPlaneManager.requestedDetectionMode = PlaneDetectionMode.Horizontal;
        }

        ShowInstructions();
    }

    /// <summary>
    /// 处理触摸输入
    /// </summary>
    void HandleTouchInput()
    {
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);

            if (touch.phase == TouchPhase.Began)
            {
                if (!isEarthPlaced)
                {
                    TryPlaceEarth(touch.position);
                }
                else
                {
                    HandleEarthInteraction(touch.position);
                }
            }
        }

        // 鼠标输入支持（用于编辑器测试）
        if (Input.GetMouseButtonDown(0))
        {
            Vector2 screenPosition = Input.mousePosition;
            if (!isEarthPlaced)
            {
                TryPlaceEarth(screenPosition);
            }
            else
            {
                HandleEarthInteraction(screenPosition);
            }
        }
    }

    /// <summary>
    /// 尝试在检测到的平面上放置地球
    /// </summary>
    void TryPlaceEarth(Vector2 screenPosition)
    {
        if (arRaycastManager.Raycast(screenPosition, raycastHits, TrackableType.PlaneWithinPolygon))
        {
            Pose hitPose = raycastHits[0].pose;
            PlaceEarth(hitPose.position, hitPose.rotation);
        }
    }

    /// <summary>
    /// 在指定位置放置地球模型
    /// </summary>
    void PlaceEarth(Vector3 position, Quaternion rotation)
    {
        if (earthPrefab == null)
        {
            Debug.LogError("地球预制体未设置！");
            return;
        }

        // 如果已经有地球实例，先销毁
        if (currentEarthInstance != null)
        {
            DestroyImmediate(currentEarthInstance);
        }

        // 创建新的地球实例
        currentEarthInstance = Instantiate(earthPrefab, position, rotation);
        
        if (earthContainer != null)
        {
            currentEarthInstance.transform.SetParent(earthContainer);
        }

        isEarthPlaced = true;
        
        // 禁用平面可视化
        SetPlanesVisible(false);
        
        Debug.Log("地球已放置在位置: " + position);
    }

    /// <summary>
    /// 处理地球交互
    /// </summary>
    void HandleEarthInteraction(Vector2 screenPosition)
    {
        Ray ray = arCamera.ScreenPointToRay(screenPosition);
        RaycastHit hit;

        if (Physics.Raycast(ray, out hit))
        {
            if (hit.collider.gameObject == currentEarthInstance || 
                hit.collider.transform.IsChildOf(currentEarthInstance.transform))
            {
                // 获取地球控制器并处理交互
                EarthController earthController = currentEarthInstance.GetComponent<EarthController>();
                if (earthController != null)
                {
                    earthController.OnEarthTouched(hit.point);
                }
            }
        }
    }

    /// <summary>
    /// 更新UI显示
    /// </summary>
    void UpdateUI()
    {
        if (planeDetectionUI != null)
        {
            planeDetectionUI.SetActive(!isEarthPlaced);
        }

        if (instructionUI != null)
        {
            instructionUI.SetActive(!isEarthPlaced);
        }
    }

    /// <summary>
    /// 显示使用说明
    /// </summary>
    void ShowInstructions()
    {
        if (instructionUI != null)
        {
            instructionUI.SetActive(true);
        }
    }

    /// <summary>
    /// 设置平面可见性
    /// </summary>
    void SetPlanesVisible(bool visible)
    {
        foreach (var plane in arPlaneManager.trackables)
        {
            plane.gameObject.SetActive(visible);
        }
    }

    /// <summary>
    /// 重置AR会话
    /// </summary>
    public void ResetARSession()
    {
        if (currentEarthInstance != null)
        {
            DestroyImmediate(currentEarthInstance);
            currentEarthInstance = null;
        }

        isEarthPlaced = false;
        SetPlanesVisible(true);
        
        // 重新启用平面检测
        if (arPlaneManager != null)
        {
            arPlaneManager.enabled = true;
        }
    }

    /// <summary>
    /// 获取当前地球实例
    /// </summary>
    public GameObject GetCurrentEarth()
    {
        return currentEarthInstance;
    }

    /// <summary>
    /// 检查地球是否已放置
    /// </summary>
    public bool IsEarthPlaced()
    {
        return isEarthPlaced;
    }
}
