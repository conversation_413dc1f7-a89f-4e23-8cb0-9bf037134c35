using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// 问题数据结构
/// </summary>
[System.Serializable]
public class QuizQuestion
{
    public string question;
    public string[] options;
    public int correctAnswer;
    public string explanation;
    public float latitude;
    public float longitude;
}

/// <summary>
/// 教学模式管理器
/// 负责管理教学演示、测验等教育功能
/// </summary>
public class TeachingModeManager : MonoBehaviour
{
    [Header("演示模式")]
    public GameObject demoPanel;
    public TextMeshProUGUI demoTitle;
    public TextMeshProUGUI demoDescription;
    public Button nextDemoButton;
    public Button prevDemoButton;

    [Header("测验模式")]
    public GameObject quizPanel;
    public TextMeshProUGUI questionText;
    public Button[] optionButtons;
    public TextMeshProUGUI scoreText;
    public TextMeshProUGUI explanationText;
    public Button nextQuestionButton;

    [Header("学习进度")]
    public GameObject progressPanel;
    public Slider progressSlider;
    public TextMeshProUGUI progressText;

    [Header("知识点标记")]
    public GameObject knowledgePointPrefab;
    public Transform knowledgePointsParent;

    private List<QuizQuestion> quizQuestions;
    private int currentQuestionIndex = 0;
    private int correctAnswers = 0;
    private int totalQuestions = 0;
    
    private List<string> demoTopics;
    private int currentDemoIndex = 0;
    
    private EarthController earthController;
    private UIManager uiManager;
    private GeographyDataManager geographyData;

    void Start()
    {
        InitializeTeachingMode();
        SetupEventListeners();
        LoadQuizQuestions();
        LoadDemoTopics();
    }

    /// <summary>
    /// 初始化教学模式
    /// </summary>
    void InitializeTeachingMode()
    {
        earthController = FindObjectOfType<EarthController>();
        uiManager = FindObjectOfType<UIManager>();
        geographyData = FindObjectOfType<GeographyDataManager>();

        if (demoPanel != null) demoPanel.SetActive(false);
        if (quizPanel != null) quizPanel.SetActive(false);
        if (progressPanel != null) progressPanel.SetActive(false);
    }

    /// <summary>
    /// 设置事件监听器
    /// </summary>
    void SetupEventListeners()
    {
        if (nextDemoButton != null)
            nextDemoButton.onClick.AddListener(NextDemo);
        
        if (prevDemoButton != null)
            prevDemoButton.onClick.AddListener(PreviousDemo);
        
        if (nextQuestionButton != null)
            nextQuestionButton.onClick.AddListener(NextQuestion);

        // 设置选项按钮
        for (int i = 0; i < optionButtons.Length; i++)
        {
            int index = i; // 闭包变量
            if (optionButtons[i] != null)
            {
                optionButtons[i].onClick.AddListener(() => OnOptionSelected(index));
            }
        }
    }

    /// <summary>
    /// 加载测验问题
    /// </summary>
    void LoadQuizQuestions()
    {
        quizQuestions = new List<QuizQuestion>
        {
            new QuizQuestion
            {
                question = "世界上面积最大的国家是？",
                options = new string[] { "中国", "美国", "俄罗斯", "加拿大" },
                correctAnswer = 2,
                explanation = "俄罗斯是世界上面积最大的国家，约1710万平方公里。",
                latitude = 60f,
                longitude = 100f
            },
            new QuizQuestion
            {
                question = "赤道穿过以下哪个大洲？",
                options = new string[] { "欧洲", "南美洲", "北美洲", "澳洲" },
                correctAnswer = 1,
                explanation = "赤道穿过南美洲、非洲和亚洲。",
                latitude = 0f,
                longitude = -60f
            },
            new QuizQuestion
            {
                question = "世界上人口最多的城市是？",
                options = new string[] { "纽约", "东京", "上海", "孟买" },
                correctAnswer = 1,
                explanation = "东京都市圈是世界上人口最多的都市圈。",
                latitude = 35.6762f,
                longitude = 139.6503f
            },
            new QuizQuestion
            {
                question = "以下哪个是世界最高峰？",
                options = new string[] { "K2峰", "珠穆朗玛峰", "干城章嘉峰", "洛子峰" },
                correctAnswer = 1,
                explanation = "珠穆朗玛峰海拔8848.86米，是世界最高峰。",
                latitude = 27.9881f,
                longitude = 86.9250f
            },
            new QuizQuestion
            {
                question = "亚马逊雨林主要位于哪个国家？",
                options = new string[] { "阿根廷", "巴西", "哥伦比亚", "秘鲁" },
                correctAnswer = 1,
                explanation = "亚马逊雨林约60%位于巴西境内。",
                latitude = -3.4653f,
                longitude = -62.2159f
            }
        };

        totalQuestions = quizQuestions.Count;
    }

    /// <summary>
    /// 加载演示主题
    /// </summary>
    void LoadDemoTopics()
    {
        demoTopics = new List<string>
        {
            "地球的结构",
            "大陆和海洋",
            "气候带分布",
            "时区系统",
            "板块构造",
            "人口分布",
            "自然资源"
        };
    }

    /// <summary>
    /// 开始演示模式
    /// </summary>
    public void StartDemoMode()
    {
        if (demoPanel != null)
        {
            demoPanel.SetActive(true);
            currentDemoIndex = 0;
            ShowCurrentDemo();
        }
    }

    /// <summary>
    /// 显示当前演示
    /// </summary>
    void ShowCurrentDemo()
    {
        if (demoTopics == null || currentDemoIndex >= demoTopics.Count) return;

        string topic = demoTopics[currentDemoIndex];
        
        if (demoTitle != null)
            demoTitle.text = topic;

        if (demoDescription != null)
        {
            demoDescription.text = GetDemoDescription(topic);
        }

        // 根据主题调整地球显示
        AdjustEarthForDemo(topic);
    }

    /// <summary>
    /// 获取演示描述
    /// </summary>
    string GetDemoDescription(string topic)
    {
        switch (topic)
        {
            case "地球的结构":
                return "地球由地壳、地幔和地核三个主要层次组成。地壳是最外层，厚度约5-70公里。";
            
            case "大陆和海洋":
                return "地球表面约71%被海洋覆盖，29%是陆地。七大洲分别是亚洲、非洲、北美洲、南美洲、南极洲、欧洲和大洋洲。";
            
            case "气候带分布":
                return "地球按纬度分为热带、亚热带、温带、亚寒带和极地气候带。气候主要受纬度、海陆分布和地形影响。";
            
            case "时区系统":
                return "全球分为24个时区，每个时区相差15度经度。国际日期变更线大致沿180度经线。";
            
            case "板块构造":
                return "地球表面由多个板块组成，板块运动导致地震、火山和山脉的形成。";
            
            case "人口分布":
                return "世界人口主要集中在亚洲东部、欧洲、北美东部等地区，受气候、地形和经济发展影响。";
            
            case "自然资源":
                return "地球拥有丰富的自然资源，包括矿物、森林、水资源等，分布不均匀。";
            
            default:
                return "这是一个关于地球地理知识的演示。";
        }
    }

    /// <summary>
    /// 根据演示主题调整地球显示
    /// </summary>
    void AdjustEarthForDemo(string topic)
    {
        if (earthController == null) return;

        switch (topic)
        {
            case "时区系统":
                // 显示时区线
                break;
            
            case "气候带分布":
                // 高亮气候带
                break;
            
            case "大陆和海洋":
                // 突出显示大陆轮廓
                break;
        }
    }

    /// <summary>
    /// 下一个演示
    /// </summary>
    public void NextDemo()
    {
        currentDemoIndex = (currentDemoIndex + 1) % demoTopics.Count;
        ShowCurrentDemo();
    }

    /// <summary>
    /// 上一个演示
    /// </summary>
    public void PreviousDemo()
    {
        currentDemoIndex = (currentDemoIndex - 1 + demoTopics.Count) % demoTopics.Count;
        ShowCurrentDemo();
    }

    /// <summary>
    /// 开始测验模式
    /// </summary>
    public void StartQuizMode()
    {
        if (quizPanel != null)
        {
            quizPanel.SetActive(true);
            currentQuestionIndex = 0;
            correctAnswers = 0;
            ShowCurrentQuestion();
        }
    }

    /// <summary>
    /// 显示当前问题
    /// </summary>
    void ShowCurrentQuestion()
    {
        if (quizQuestions == null || currentQuestionIndex >= quizQuestions.Count) return;

        QuizQuestion question = quizQuestions[currentQuestionIndex];
        
        if (questionText != null)
            questionText.text = question.question;

        // 设置选项按钮
        for (int i = 0; i < optionButtons.Length && i < question.options.Length; i++)
        {
            if (optionButtons[i] != null)
            {
                optionButtons[i].gameObject.SetActive(true);
                TextMeshProUGUI buttonText = optionButtons[i].GetComponentInChildren<TextMeshProUGUI>();
                if (buttonText != null)
                {
                    buttonText.text = question.options[i];
                }
                
                // 重置按钮颜色
                optionButtons[i].GetComponent<Image>().color = Color.white;
            }
        }

        // 隐藏多余的按钮
        for (int i = question.options.Length; i < optionButtons.Length; i++)
        {
            if (optionButtons[i] != null)
            {
                optionButtons[i].gameObject.SetActive(false);
            }
        }

        // 隐藏解释和下一题按钮
        if (explanationText != null)
            explanationText.gameObject.SetActive(false);
        
        if (nextQuestionButton != null)
            nextQuestionButton.gameObject.SetActive(false);

        // 更新分数显示
        UpdateScoreDisplay();
        
        // 移动地球到相关位置
        MoveEarthToQuestion(question);
    }

    /// <summary>
    /// 选项被选择
    /// </summary>
    void OnOptionSelected(int optionIndex)
    {
        if (currentQuestionIndex >= quizQuestions.Count) return;

        QuizQuestion question = quizQuestions[currentQuestionIndex];
        bool isCorrect = optionIndex == question.correctAnswer;

        // 更新按钮颜色
        for (int i = 0; i < optionButtons.Length; i++)
        {
            if (optionButtons[i] != null && optionButtons[i].gameObject.activeSelf)
            {
                Image buttonImage = optionButtons[i].GetComponent<Image>();
                if (i == question.correctAnswer)
                {
                    buttonImage.color = Color.green;
                }
                else if (i == optionIndex && !isCorrect)
                {
                    buttonImage.color = Color.red;
                }
                
                // 禁用按钮
                optionButtons[i].interactable = false;
            }
        }

        if (isCorrect)
        {
            correctAnswers++;
        }

        // 显示解释
        if (explanationText != null)
        {
            explanationText.text = question.explanation;
            explanationText.gameObject.SetActive(true);
        }

        // 显示下一题按钮
        if (nextQuestionButton != null)
        {
            nextQuestionButton.gameObject.SetActive(true);
        }

        UpdateScoreDisplay();
    }

    /// <summary>
    /// 下一题
    /// </summary>
    public void NextQuestion()
    {
        currentQuestionIndex++;
        
        if (currentQuestionIndex >= quizQuestions.Count)
        {
            ShowQuizResults();
        }
        else
        {
            // 重新启用按钮
            foreach (var button in optionButtons)
            {
                if (button != null)
                {
                    button.interactable = true;
                }
            }
            
            ShowCurrentQuestion();
        }
    }

    /// <summary>
    /// 显示测验结果
    /// </summary>
    void ShowQuizResults()
    {
        float percentage = (float)correctAnswers / totalQuestions * 100f;
        
        if (questionText != null)
        {
            questionText.text = $"测验完成！\n\n正确率: {percentage:F1}%\n({correctAnswers}/{totalQuestions})";
        }

        // 隐藏选项按钮
        foreach (var button in optionButtons)
        {
            if (button != null)
            {
                button.gameObject.SetActive(false);
            }
        }

        if (explanationText != null)
        {
            string feedback = "";
            if (percentage >= 80f)
                feedback = "优秀！你对地理知识掌握得很好！";
            else if (percentage >= 60f)
                feedback = "不错！继续学习会更好！";
            else
                feedback = "需要加强学习，多了解地理知识！";
            
            explanationText.text = feedback;
            explanationText.gameObject.SetActive(true);
        }

        if (nextQuestionButton != null)
        {
            nextQuestionButton.gameObject.SetActive(false);
        }
    }

    /// <summary>
    /// 更新分数显示
    /// </summary>
    void UpdateScoreDisplay()
    {
        if (scoreText != null)
        {
            scoreText.text = $"分数: {correctAnswers}/{currentQuestionIndex + 1}";
        }
    }

    /// <summary>
    /// 移动地球到问题相关位置
    /// </summary>
    void MoveEarthToQuestion(QuizQuestion question)
    {
        // 这里可以实现地球旋转到问题相关的地理位置
        // 例如，如果问题是关于某个国家，就旋转地球显示该国家
    }

    /// <summary>
    /// 关闭教学模式
    /// </summary>
    public void CloseTeachingMode()
    {
        if (demoPanel != null) demoPanel.SetActive(false);
        if (quizPanel != null) quizPanel.SetActive(false);
        if (progressPanel != null) progressPanel.SetActive(false);
    }
}
