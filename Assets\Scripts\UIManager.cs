using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

/// <summary>
/// UI管理器
/// 负责管理所有用户界面元素和交互
/// </summary>
public class UIManager : MonoBehaviour
{
    [Header("主界面")]
    public Canvas mainCanvas;
    public GameObject mainMenu;
    public GameObject settingsPanel;
    public GameObject helpPanel;

    [Header("AR界面")]
    public GameObject arInstructionPanel;
    public TextMeshProUGUI instructionText;
    public GameObject planeDetectionIndicator;

    [Header("信息面板")]
    public GameObject locationInfoPanel;
    public TextMeshProUGUI locationTitle;
    public TextMeshProUGUI locationDetails;
    public Image countryFlag;
    public Button closeInfoButton;

    [Header("搜索功能")]
    public GameObject searchPanel;
    public TMP_InputField searchInput;
    public Button searchButton;
    public Transform searchResultsParent;
    public GameObject searchResultPrefab;

    [Header("控制面板")]
    public GameObject controlPanel;
    public Button resetButton;
    public Button autoRotateButton;
    public Slider scaleSlider;
    public Toggle dayNightToggle;

    [Header("教学模式")]
    public GameObject teachingModePanel;
    public Button quizButton;
    public Button demoButton;
    public GameObject quizPanel;

    [Header("设置")]
    public Slider rotationSpeedSlider;
    public Toggle soundToggle;
    public Dropdown languageDropdown;

    private ARManager arManager;
    private EarthController earthController;
    private GeographyDataManager geographyData;
    private bool isLocationInfoVisible = false;

    void Start()
    {
        InitializeUI();
        SetupEventListeners();
        FindComponents();
    }

    /// <summary>
    /// 初始化UI
    /// </summary>
    void InitializeUI()
    {
        // 初始状态设置
        if (mainMenu != null) mainMenu.SetActive(true);
        if (settingsPanel != null) settingsPanel.SetActive(false);
        if (helpPanel != null) helpPanel.SetActive(false);
        if (locationInfoPanel != null) locationInfoPanel.SetActive(false);
        if (searchPanel != null) searchPanel.SetActive(false);
        if (teachingModePanel != null) teachingModePanel.SetActive(false);
        if (quizPanel != null) quizPanel.SetActive(false);

        // 显示AR使用说明
        ShowARInstructions();
    }

    /// <summary>
    /// 设置事件监听器
    /// </summary>
    void SetupEventListeners()
    {
        // 信息面板
        if (closeInfoButton != null)
            closeInfoButton.onClick.AddListener(HideLocationInfo);

        // 搜索功能
        if (searchButton != null)
            searchButton.onClick.AddListener(OnSearchButtonClicked);
        
        if (searchInput != null)
            searchInput.onEndEdit.AddListener(OnSearchInputSubmit);

        // 控制面板
        if (resetButton != null)
            resetButton.onClick.AddListener(OnResetButtonClicked);
        
        if (autoRotateButton != null)
            autoRotateButton.onClick.AddListener(OnAutoRotateButtonClicked);
        
        if (scaleSlider != null)
            scaleSlider.onValueChanged.AddListener(OnScaleSliderChanged);
        
        if (dayNightToggle != null)
            dayNightToggle.onValueChanged.AddListener(OnDayNightToggleChanged);

        // 教学模式
        if (quizButton != null)
            quizButton.onClick.AddListener(OnQuizButtonClicked);
        
        if (demoButton != null)
            demoButton.onClick.AddListener(OnDemoButtonClicked);

        // 设置
        if (rotationSpeedSlider != null)
            rotationSpeedSlider.onValueChanged.AddListener(OnRotationSpeedChanged);
    }

    /// <summary>
    /// 查找组件引用
    /// </summary>
    void FindComponents()
    {
        arManager = FindObjectOfType<ARManager>();
        earthController = FindObjectOfType<EarthController>();
        geographyData = FindObjectOfType<GeographyDataManager>();
    }

    /// <summary>
    /// 显示AR使用说明
    /// </summary>
    void ShowARInstructions()
    {
        if (arInstructionPanel != null)
        {
            arInstructionPanel.SetActive(true);
            if (instructionText != null)
            {
                instructionText.text = "请将设备摄像头对准平坦表面\n等待检测到平面后点击屏幕放置地球";
            }
        }
    }

    /// <summary>
    /// 隐藏AR使用说明
    /// </summary>
    public void HideARInstructions()
    {
        if (arInstructionPanel != null)
        {
            arInstructionPanel.SetActive(false);
        }
        
        // 显示控制面板
        if (controlPanel != null)
        {
            controlPanel.SetActive(true);
        }
    }

    /// <summary>
    /// 显示位置信息
    /// </summary>
    public void ShowLocationInfo(LocationInfo locationInfo)
    {
        if (locationInfoPanel == null || locationInfo == null) return;

        locationInfoPanel.SetActive(true);
        isLocationInfoVisible = true;

        // 设置标题
        if (locationTitle != null)
        {
            string title = !string.IsNullOrEmpty(locationInfo.cityName) 
                ? locationInfo.cityName 
                : $"{locationInfo.latitude:F2}°, {locationInfo.longitude:F2}°";
            locationTitle.text = title;
        }

        // 设置详细信息
        if (locationDetails != null)
        {
            string details = "";
            
            if (!string.IsNullOrEmpty(locationInfo.countryName))
                details += $"国家: {locationInfo.countryName}\n";
            
            if (!string.IsNullOrEmpty(locationInfo.continent))
                details += $"大洲: {locationInfo.continent}\n";
            
            details += $"坐标: {locationInfo.latitude:F2}°, {locationInfo.longitude:F2}°\n";
            
            if (locationInfo.population > 0)
                details += $"人口: {locationInfo.population:N0}\n";
            
            if (!string.IsNullOrEmpty(locationInfo.climate))
                details += $"气候: {locationInfo.climate}\n";
            
            if (!string.IsNullOrEmpty(locationInfo.timeZone))
                details += $"时区: {locationInfo.timeZone}\n";
            
            if (locationInfo.elevation != 0)
                details += $"海拔: {locationInfo.elevation:F1}米\n";
            
            if (!string.IsNullOrEmpty(locationInfo.description))
                details += $"\n{locationInfo.description}";

            locationDetails.text = details;
        }

        // 自动隐藏
        StartCoroutine(AutoHideLocationInfo());
    }

    /// <summary>
    /// 隐藏位置信息
    /// </summary>
    public void HideLocationInfo()
    {
        if (locationInfoPanel != null)
        {
            locationInfoPanel.SetActive(false);
            isLocationInfoVisible = false;
        }
    }

    /// <summary>
    /// 自动隐藏位置信息
    /// </summary>
    IEnumerator AutoHideLocationInfo()
    {
        yield return new WaitForSeconds(8f);
        if (isLocationInfoVisible)
        {
            HideLocationInfo();
        }
    }

    /// <summary>
    /// 搜索按钮点击事件
    /// </summary>
    void OnSearchButtonClicked()
    {
        if (searchInput != null && !string.IsNullOrEmpty(searchInput.text))
        {
            PerformSearch(searchInput.text);
        }
    }

    /// <summary>
    /// 搜索输入提交事件
    /// </summary>
    void OnSearchInputSubmit(string searchTerm)
    {
        if (!string.IsNullOrEmpty(searchTerm))
        {
            PerformSearch(searchTerm);
        }
    }

    /// <summary>
    /// 执行搜索
    /// </summary>
    void PerformSearch(string searchTerm)
    {
        if (geographyData == null) return;

        var results = geographyData.SearchLocations(searchTerm);
        DisplaySearchResults(results);
    }

    /// <summary>
    /// 显示搜索结果
    /// </summary>
    void DisplaySearchResults(System.Collections.Generic.List<LocationInfo> results)
    {
        // 清除之前的结果
        if (searchResultsParent != null)
        {
            foreach (Transform child in searchResultsParent)
            {
                Destroy(child.gameObject);
            }
        }

        // 显示新结果
        foreach (var result in results)
        {
            if (searchResultPrefab != null && searchResultsParent != null)
            {
                GameObject resultItem = Instantiate(searchResultPrefab, searchResultsParent);
                
                // 设置结果项内容
                TextMeshProUGUI resultText = resultItem.GetComponentInChildren<TextMeshProUGUI>();
                if (resultText != null)
                {
                    resultText.text = $"{result.cityName}, {result.countryName}";
                }

                // 添加点击事件
                Button resultButton = resultItem.GetComponent<Button>();
                if (resultButton != null)
                {
                    resultButton.onClick.AddListener(() => OnSearchResultClicked(result));
                }
            }
        }

        // 显示搜索面板
        if (searchPanel != null)
        {
            searchPanel.SetActive(true);
        }
    }

    /// <summary>
    /// 搜索结果点击事件
    /// </summary>
    void OnSearchResultClicked(LocationInfo location)
    {
        ShowLocationInfo(location);
        
        // 隐藏搜索面板
        if (searchPanel != null)
        {
            searchPanel.SetActive(false);
        }
    }

    /// <summary>
    /// 重置按钮点击事件
    /// </summary>
    void OnResetButtonClicked()
    {
        if (earthController != null)
        {
            earthController.ResetEarth();
        }
    }

    /// <summary>
    /// 自动旋转按钮点击事件
    /// </summary>
    void OnAutoRotateButtonClicked()
    {
        if (earthController != null)
        {
            earthController.SetAutoRotate(!earthController.autoRotate);
        }
    }

    /// <summary>
    /// 缩放滑块变化事件
    /// </summary>
    void OnScaleSliderChanged(float value)
    {
        // 实现缩放逻辑
    }

    /// <summary>
    /// 昼夜切换事件
    /// </summary>
    void OnDayNightToggleChanged(bool isOn)
    {
        if (earthController != null)
        {
            earthController.enableDayNightCycle = isOn;
        }
    }

    /// <summary>
    /// 测验按钮点击事件
    /// </summary>
    void OnQuizButtonClicked()
    {
        if (quizPanel != null)
        {
            quizPanel.SetActive(true);
        }
    }

    /// <summary>
    /// 演示按钮点击事件
    /// </summary>
    void OnDemoButtonClicked()
    {
        // 实现演示模式
    }

    /// <summary>
    /// 旋转速度变化事件
    /// </summary>
    void OnRotationSpeedChanged(float value)
    {
        if (earthController != null)
        {
            earthController.rotationSpeed = value;
        }
    }

    /// <summary>
    /// 切换面板显示状态
    /// </summary>
    public void TogglePanel(GameObject panel)
    {
        if (panel != null)
        {
            panel.SetActive(!panel.activeSelf);
        }
    }
}
