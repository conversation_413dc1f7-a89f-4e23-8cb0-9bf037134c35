using UnityEngine;
using System.Collections.Generic;
using System;

/// <summary>
/// 地理位置信息数据结构
/// </summary>
[System.Serializable]
public class LocationInfo
{
    public string countryName;
    public string cityName;
    public string continent;
    public float latitude;
    public float longitude;
    public long population;
    public string climate;
    public string timeZone;
    public string currency;
    public string language;
    public float elevation;
    public string description;
}

/// <summary>
/// 国家信息数据结构
/// </summary>
[System.Serializable]
public class CountryInfo
{
    public string name;
    public string capital;
    public string continent;
    public long population;
    public float area; // 平方公里
    public string currency;
    public string[] languages;
    public string flag;
    public string description;
    public Vector2 centerCoordinate;
}

/// <summary>
/// 地理数据管理器
/// 负责管理和提供地理信息数据
/// </summary>
public class GeographyDataManager : MonoBehaviour
{
    [Header("数据文件")]
    public TextAsset countriesDataFile;
    public TextAsset citiesDataFile;
    public TextAsset climateDataFile;

    private Dictionary<string, CountryInfo> countries;
    private List<LocationInfo> cities;
    private Dictionary<Vector2, string> climateZones;

    void Start()
    {
        InitializeData();
    }

    /// <summary>
    /// 初始化地理数据
    /// </summary>
    void InitializeData()
    {
        LoadCountriesData();
        LoadCitiesData();
        LoadClimateData();
        
        Debug.Log($"地理数据加载完成 - 国家: {countries.Count}, 城市: {cities.Count}");
    }

    /// <summary>
    /// 加载国家数据
    /// </summary>
    void LoadCountriesData()
    {
        countries = new Dictionary<string, CountryInfo>();

        // 添加一些示例国家数据
        AddCountry(new CountryInfo
        {
            name = "中国",
            capital = "北京",
            continent = "亚洲",
            population = 1400000000,
            area = 9596960,
            currency = "人民币",
            languages = new string[] { "中文" },
            description = "世界上人口最多的国家，拥有悠久的历史和灿烂的文化。",
            centerCoordinate = new Vector2(35.0f, 104.0f)
        });

        AddCountry(new CountryInfo
        {
            name = "美国",
            capital = "华盛顿",
            continent = "北美洲",
            population = 331000000,
            area = 9833517,
            currency = "美元",
            languages = new string[] { "英语" },
            description = "世界上最大的经济体，由50个州组成的联邦共和国。",
            centerCoordinate = new Vector2(40.0f, -100.0f)
        });

        AddCountry(new CountryInfo
        {
            name = "俄罗斯",
            capital = "莫斯科",
            continent = "欧洲/亚洲",
            population = 146000000,
            area = 17098242,
            currency = "卢布",
            languages = new string[] { "俄语" },
            description = "世界上面积最大的国家，横跨欧亚大陆。",
            centerCoordinate = new Vector2(60.0f, 100.0f)
        });

        AddCountry(new CountryInfo
        {
            name = "巴西",
            capital = "巴西利亚",
            continent = "南美洲",
            population = 213000000,
            area = 8514877,
            currency = "雷亚尔",
            languages = new string[] { "葡萄牙语" },
            description = "南美洲最大的国家，拥有亚马逊雨林的大部分区域。",
            centerCoordinate = new Vector2(-10.0f, -55.0f)
        });
    }

    /// <summary>
    /// 加载城市数据
    /// </summary>
    void LoadCitiesData()
    {
        cities = new List<LocationInfo>();

        // 添加一些示例城市数据
        AddCity(new LocationInfo
        {
            countryName = "中国",
            cityName = "北京",
            continent = "亚洲",
            latitude = 39.9042f,
            longitude = 116.4074f,
            population = 21540000,
            climate = "温带季风气候",
            timeZone = "UTC+8",
            currency = "人民币",
            language = "中文",
            elevation = 43.5f,
            description = "中国的首都，政治、文化和教育中心。"
        });

        AddCity(new LocationInfo
        {
            countryName = "中国",
            cityName = "上海",
            continent = "亚洲",
            latitude = 31.2304f,
            longitude = 121.4737f,
            population = 24280000,
            climate = "亚热带季风气候",
            timeZone = "UTC+8",
            currency = "人民币",
            language = "中文",
            elevation = 4.0f,
            description = "中国最大的城市，重要的经济和金融中心。"
        });

        AddCity(new LocationInfo
        {
            countryName = "美国",
            cityName = "纽约",
            continent = "北美洲",
            latitude = 40.7128f,
            longitude = -74.0060f,
            population = 8400000,
            climate = "温带大陆性气候",
            timeZone = "UTC-5",
            currency = "美元",
            language = "英语",
            elevation = 10.0f,
            description = "美国最大的城市，世界金融中心之一。"
        });

        AddCity(new LocationInfo
        {
            countryName = "英国",
            cityName = "伦敦",
            continent = "欧洲",
            latitude = 51.5074f,
            longitude = -0.1278f,
            population = 9000000,
            climate = "温带海洋性气候",
            timeZone = "UTC+0",
            currency = "英镑",
            language = "英语",
            elevation = 35.0f,
            description = "英国首都，历史悠久的国际大都市。"
        });
    }

    /// <summary>
    /// 加载气候数据
    /// </summary>
    void LoadClimateData()
    {
        climateZones = new Dictionary<Vector2, string>();
        
        // 简化的气候带数据
        // 实际应用中应该使用更详细的气候数据
    }

    /// <summary>
    /// 添加国家信息
    /// </summary>
    void AddCountry(CountryInfo country)
    {
        if (!countries.ContainsKey(country.name))
        {
            countries.Add(country.name, country);
        }
    }

    /// <summary>
    /// 添加城市信息
    /// </summary>
    void AddCity(LocationInfo city)
    {
        cities.Add(city);
    }

    /// <summary>
    /// 根据经纬度获取位置信息
    /// </summary>
    public LocationInfo GetLocationInfo(float latitude, float longitude)
    {
        // 查找最近的城市
        LocationInfo nearestCity = null;
        float minDistance = float.MaxValue;

        foreach (var city in cities)
        {
            float distance = CalculateDistance(latitude, longitude, city.latitude, city.longitude);
            if (distance < minDistance)
            {
                minDistance = distance;
                nearestCity = city;
            }
        }

        // 如果距离太远，返回基本地理信息
        if (minDistance > 500f) // 500公里
        {
            return new LocationInfo
            {
                latitude = latitude,
                longitude = longitude,
                climate = GetClimateByLatitude(latitude),
                timeZone = GetTimeZoneByLongitude(longitude),
                description = $"位置: {latitude:F2}°, {longitude:F2}°"
            };
        }

        return nearestCity;
    }

    /// <summary>
    /// 根据国家名称获取国家信息
    /// </summary>
    public CountryInfo GetCountryInfo(string countryName)
    {
        countries.TryGetValue(countryName, out CountryInfo country);
        return country;
    }

    /// <summary>
    /// 搜索位置
    /// </summary>
    public List<LocationInfo> SearchLocations(string searchTerm)
    {
        List<LocationInfo> results = new List<LocationInfo>();
        
        foreach (var city in cities)
        {
            if (city.cityName.ToLower().Contains(searchTerm.ToLower()) ||
                city.countryName.ToLower().Contains(searchTerm.ToLower()))
            {
                results.Add(city);
            }
        }

        return results;
    }

    /// <summary>
    /// 计算两点间距离（公里）
    /// </summary>
    float CalculateDistance(float lat1, float lon1, float lat2, float lon2)
    {
        const float R = 6371f; // 地球半径（公里）
        
        float dLat = (lat2 - lat1) * Mathf.Deg2Rad;
        float dLon = (lon2 - lon1) * Mathf.Deg2Rad;
        
        float a = Mathf.Sin(dLat / 2) * Mathf.Sin(dLat / 2) +
                  Mathf.Cos(lat1 * Mathf.Deg2Rad) * Mathf.Cos(lat2 * Mathf.Deg2Rad) *
                  Mathf.Sin(dLon / 2) * Mathf.Sin(dLon / 2);
        
        float c = 2 * Mathf.Atan2(Mathf.Sqrt(a), Mathf.Sqrt(1 - a));
        
        return R * c;
    }

    /// <summary>
    /// 根据纬度获取气候类型
    /// </summary>
    string GetClimateByLatitude(float latitude)
    {
        float absLat = Mathf.Abs(latitude);
        
        if (absLat >= 66.5f) return "极地气候";
        if (absLat >= 60f) return "亚寒带气候";
        if (absLat >= 40f) return "温带气候";
        if (absLat >= 23.5f) return "亚热带气候";
        return "热带气候";
    }

    /// <summary>
    /// 根据经度获取时区
    /// </summary>
    string GetTimeZoneByLongitude(float longitude)
    {
        int timeZone = Mathf.RoundToInt(longitude / 15f);
        return $"UTC{(timeZone >= 0 ? "+" : "")}{timeZone}";
    }
}
