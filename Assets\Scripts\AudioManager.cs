using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// 音频类型枚举
/// </summary>
public enum AudioType
{
    UI,
    Interaction,
    Ambient,
    Voice
}

/// <summary>
/// 音频剪辑数据
/// </summary>
[System.Serializable]
public class AudioClipData
{
    public string name;
    public AudioClip clip;
    public AudioType type;
    public float volume = 1.0f;
    public bool loop = false;
}

/// <summary>
/// 音效管理器
/// 负责管理游戏中的所有音效和背景音乐
/// </summary>
public class AudioManager : MonoBehaviour
{
    [Header("音频源")]
    public AudioSource uiAudioSource;
    public AudioSource interactionAudioSource;
    public AudioSource ambientAudioSource;
    public AudioSource voiceAudioSource;

    [Header("音频剪辑")]
    public AudioClipData[] audioClips;

    [Header("音量设置")]
    [Range(0f, 1f)]
    public float masterVolume = 1.0f;
    [Range(0f, 1f)]
    public float uiVolume = 1.0f;
    [Range(0f, 1f)]
    public float interactionVolume = 1.0f;
    [Range(0f, 1f)]
    public float ambientVolume = 0.5f;
    [Range(0f, 1f)]
    public float voiceVolume = 1.0f;

    [Header("设置")]
    public bool enableSound = true;
    public bool enableVoice = true;

    private Dictionary<string, AudioClipData> audioClipDict;
    private static AudioManager instance;

    public static AudioManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindObjectOfType<AudioManager>();
            }
            return instance;
        }
    }

    void Awake()
    {
        // 单例模式
        if (instance == null)
        {
            instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeAudioManager();
        }
        else if (instance != this)
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        LoadAudioSettings();
        PlayAmbientSound();
    }

    /// <summary>
    /// 初始化音频管理器
    /// </summary>
    void InitializeAudioManager()
    {
        // 创建音频剪辑字典
        audioClipDict = new Dictionary<string, AudioClipData>();
        foreach (var clipData in audioClips)
        {
            if (!audioClipDict.ContainsKey(clipData.name))
            {
                audioClipDict.Add(clipData.name, clipData);
            }
        }

        // 确保音频源存在
        EnsureAudioSources();
        
        Debug.Log($"音频管理器初始化完成，加载了 {audioClips.Length} 个音频剪辑");
    }

    /// <summary>
    /// 确保音频源存在
    /// </summary>
    void EnsureAudioSources()
    {
        if (uiAudioSource == null)
        {
            GameObject uiAudioGO = new GameObject("UI Audio Source");
            uiAudioGO.transform.SetParent(transform);
            uiAudioSource = uiAudioGO.AddComponent<AudioSource>();
        }

        if (interactionAudioSource == null)
        {
            GameObject interactionAudioGO = new GameObject("Interaction Audio Source");
            interactionAudioGO.transform.SetParent(transform);
            interactionAudioSource = interactionAudioGO.AddComponent<AudioSource>();
        }

        if (ambientAudioSource == null)
        {
            GameObject ambientAudioGO = new GameObject("Ambient Audio Source");
            ambientAudioGO.transform.SetParent(transform);
            ambientAudioSource = ambientAudioGO.AddComponent<AudioSource>();
            ambientAudioSource.loop = true;
        }

        if (voiceAudioSource == null)
        {
            GameObject voiceAudioGO = new GameObject("Voice Audio Source");
            voiceAudioGO.transform.SetParent(transform);
            voiceAudioSource = voiceAudioGO.AddComponent<AudioSource>();
        }
    }

    /// <summary>
    /// 播放音效
    /// </summary>
    public void PlaySound(string soundName)
    {
        if (!enableSound || !audioClipDict.ContainsKey(soundName))
        {
            if (!audioClipDict.ContainsKey(soundName))
                Debug.LogWarning($"音频剪辑 '{soundName}' 未找到");
            return;
        }

        AudioClipData clipData = audioClipDict[soundName];
        AudioSource audioSource = GetAudioSourceByType(clipData.type);

        if (audioSource != null && clipData.clip != null)
        {
            audioSource.volume = clipData.volume * GetVolumeByType(clipData.type) * masterVolume;
            audioSource.PlayOneShot(clipData.clip);
        }
    }

    /// <summary>
    /// 播放循环音效
    /// </summary>
    public void PlayLoopSound(string soundName)
    {
        if (!enableSound || !audioClipDict.ContainsKey(soundName)) return;

        AudioClipData clipData = audioClipDict[soundName];
        AudioSource audioSource = GetAudioSourceByType(clipData.type);

        if (audioSource != null && clipData.clip != null)
        {
            audioSource.clip = clipData.clip;
            audioSource.volume = clipData.volume * GetVolumeByType(clipData.type) * masterVolume;
            audioSource.loop = true;
            audioSource.Play();
        }
    }

    /// <summary>
    /// 停止音效
    /// </summary>
    public void StopSound(AudioType audioType)
    {
        AudioSource audioSource = GetAudioSourceByType(audioType);
        if (audioSource != null)
        {
            audioSource.Stop();
        }
    }

    /// <summary>
    /// 播放语音
    /// </summary>
    public void PlayVoice(string voiceName)
    {
        if (!enableVoice || !enableSound) return;
        
        PlaySound(voiceName);
    }

    /// <summary>
    /// 播放环境音效
    /// </summary>
    void PlayAmbientSound()
    {
        if (enableSound && ambientAudioSource != null)
        {
            // 这里可以播放背景环境音效
            // PlayLoopSound("ambient_space");
        }
    }

    /// <summary>
    /// 根据类型获取音频源
    /// </summary>
    AudioSource GetAudioSourceByType(AudioType audioType)
    {
        switch (audioType)
        {
            case AudioType.UI:
                return uiAudioSource;
            case AudioType.Interaction:
                return interactionAudioSource;
            case AudioType.Ambient:
                return ambientAudioSource;
            case AudioType.Voice:
                return voiceAudioSource;
            default:
                return uiAudioSource;
        }
    }

    /// <summary>
    /// 根据类型获取音量
    /// </summary>
    float GetVolumeByType(AudioType audioType)
    {
        switch (audioType)
        {
            case AudioType.UI:
                return uiVolume;
            case AudioType.Interaction:
                return interactionVolume;
            case AudioType.Ambient:
                return ambientVolume;
            case AudioType.Voice:
                return voiceVolume;
            default:
                return 1.0f;
        }
    }

    /// <summary>
    /// 设置主音量
    /// </summary>
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        UpdateAllVolumes();
        SaveAudioSettings();
    }

    /// <summary>
    /// 设置UI音量
    /// </summary>
    public void SetUIVolume(float volume)
    {
        uiVolume = Mathf.Clamp01(volume);
        SaveAudioSettings();
    }

    /// <summary>
    /// 设置交互音量
    /// </summary>
    public void SetInteractionVolume(float volume)
    {
        interactionVolume = Mathf.Clamp01(volume);
        SaveAudioSettings();
    }

    /// <summary>
    /// 设置环境音量
    /// </summary>
    public void SetAmbientVolume(float volume)
    {
        ambientVolume = Mathf.Clamp01(volume);
        if (ambientAudioSource != null)
        {
            ambientAudioSource.volume = ambientVolume * masterVolume;
        }
        SaveAudioSettings();
    }

    /// <summary>
    /// 设置语音音量
    /// </summary>
    public void SetVoiceVolume(float volume)
    {
        voiceVolume = Mathf.Clamp01(volume);
        SaveAudioSettings();
    }

    /// <summary>
    /// 启用/禁用音效
    /// </summary>
    public void SetSoundEnabled(bool enabled)
    {
        enableSound = enabled;
        if (!enabled)
        {
            StopAllSounds();
        }
        else
        {
            PlayAmbientSound();
        }
        SaveAudioSettings();
    }

    /// <summary>
    /// 启用/禁用语音
    /// </summary>
    public void SetVoiceEnabled(bool enabled)
    {
        enableVoice = enabled;
        if (!enabled && voiceAudioSource != null)
        {
            voiceAudioSource.Stop();
        }
        SaveAudioSettings();
    }

    /// <summary>
    /// 更新所有音量
    /// </summary>
    void UpdateAllVolumes()
    {
        if (ambientAudioSource != null && ambientAudioSource.isPlaying)
        {
            ambientAudioSource.volume = ambientVolume * masterVolume;
        }
    }

    /// <summary>
    /// 停止所有音效
    /// </summary>
    void StopAllSounds()
    {
        if (uiAudioSource != null) uiAudioSource.Stop();
        if (interactionAudioSource != null) interactionAudioSource.Stop();
        if (ambientAudioSource != null) ambientAudioSource.Stop();
        if (voiceAudioSource != null) voiceAudioSource.Stop();
    }

    /// <summary>
    /// 保存音频设置
    /// </summary>
    void SaveAudioSettings()
    {
        PlayerPrefs.SetFloat("MasterVolume", masterVolume);
        PlayerPrefs.SetFloat("UIVolume", uiVolume);
        PlayerPrefs.SetFloat("InteractionVolume", interactionVolume);
        PlayerPrefs.SetFloat("AmbientVolume", ambientVolume);
        PlayerPrefs.SetFloat("VoiceVolume", voiceVolume);
        PlayerPrefs.SetInt("EnableSound", enableSound ? 1 : 0);
        PlayerPrefs.SetInt("EnableVoice", enableVoice ? 1 : 0);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// 加载音频设置
    /// </summary>
    void LoadAudioSettings()
    {
        masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1.0f);
        uiVolume = PlayerPrefs.GetFloat("UIVolume", 1.0f);
        interactionVolume = PlayerPrefs.GetFloat("InteractionVolume", 1.0f);
        ambientVolume = PlayerPrefs.GetFloat("AmbientVolume", 0.5f);
        voiceVolume = PlayerPrefs.GetFloat("VoiceVolume", 1.0f);
        enableSound = PlayerPrefs.GetInt("EnableSound", 1) == 1;
        enableVoice = PlayerPrefs.GetInt("EnableVoice", 1) == 1;
    }

    /// <summary>
    /// 播放UI音效的便捷方法
    /// </summary>
    public void PlayUISound(string soundName)
    {
        PlaySound(soundName);
    }

    /// <summary>
    /// 播放交互音效的便捷方法
    /// </summary>
    public void PlayInteractionSound(string soundName)
    {
        PlaySound(soundName);
    }
}
